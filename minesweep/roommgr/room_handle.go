package roommgr

import (
	"fmt"
	"minesweep/common/logx"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/usermgr"
	"sort"
	"time"

	"github.com/mitchellh/mapstructure"
)

// ProcessMessage 处理客户端请求
// 统一的消息分发中心：联机模式和关卡模式都通过此方法处理房间内消息
func (slf *Room) ProcessMessage(msg *request.PackMessage) {
	switch msg.MsgID {
	case constvar.MsgTypeBuyProduct:
		slf.OnBuyProduct(msg)
	case constvar.MsgTypeSetSkin:
		slf.OnSetSkin(msg)
	case constvar.MsgTypeViewerList:
		slf.OnViewerList(msg)
	case constvar.MsgTypeLeaveRoom:
		slf.OnUserLeave(msg)
	case constvar.MsgTypeUserOffline:
		slf.OnUserOffline(msg)
	case constvar.MsgTypeEnterRoom:
		slf.OnEnterRoom(msg)
	case constvar.MsgTypeFirstMoveEnd:
		slf.OnUserFirstMoveEnd(msg)
	case constvar.MsgTypeRollDice:
		slf.OnUserRollDice(msg)
	case constvar.MsgTypeMoveChessEnd:
		slf.OnUserMoveChessEnd(msg)
	case constvar.MsgTypeChoiceProp:
		slf.OnUserChoiceProp(msg)
	case constvar.MsgTypeUseProp:
		slf.OnUserUseProp(msg)
	case constvar.MsgTypeChoiceAdvance:
		slf.OnUserChoiceAdvance(msg)
	case constvar.MsgTypeReqSmartOp:
		slf.OnReqSmartOp(msg)
	case constvar.MsgTypeForceCloseGame:
		slf.OnForceCloseGame()
	case constvar.MsgTypeForceUserLeave:
		slf.OnUserForceLeave(msg)
	// 扫雷游戏消息路由
	case constvar.MsgTypeClickBlock:
		//处理用户点击方块操作（联机模式）
		slf.OnUserClickBlock(msg)
	case constvar.MsgTypeLevelClickBlock:
		//处理关卡模式点击方块操作
		slf.OnLevelClickBlock(msg)
	// 单机关卡系统消息路由
	case constvar.MsgTypeLevelProgress:
		slf.OnLevelProgress(msg)
	// AI托管系统消息路由
	case constvar.MsgTypeCancelAIManagement:
		slf.OnCancelAIManagement(msg)
	// 调试接口（仅用于开发测试，生产环境应禁用）
	case constvar.MsgTypeDebugShowMines:
		slf.OnDebugShowMines(msg)
	default:
	}
}

// OnEnterRoom 断线重连走这个接口，发送桌面的所有信息
func (slf *Room) OnEnterRoom(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 语聊房断线重连，加入旁观者(已经在房间的不会被覆盖)
	if slf.voiceRoom != nil && user.PlatRoomID == slf.voiceRoom.GetVoiceRoomId() {
		slf.ViewerJoin(&RoomUser{
			PlatRoomID: user.PlatRoomID,
			NickName:   user.Nickname,
			Avatar:     user.Avatar,
			UserID:     user.UserID,
		})
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	// 通过api创建的游戏，初始化roomUser时可能没有ssToken(如imo渠道)
	roomUser.UpdateSSToken(user.SSToken)
	slf.UpdateLastMsg(roomUser)
	slf.UpdateAllConnSrv(user.UserID)

	// 更新玩家金币
	slf.UpdateBalance(user.UserID)

	// 返回游戏桌面信息
	users := slf.buildUsersResponse(user.UserID)
	sort.Slice(users, func(i, j int) bool {
		return users[i].Pos < users[j].Pos
	})

	// 根据地图类型决定返回的数据结构
	var validHexCoords []*response.HexCoord
	switch slf.MapType {
	case constvar.MapTypeGrid:
		// 方格地图：不返回ValidHexCoords
		validHexCoords = nil
	case constvar.MapTypeHexagon:
		// 六边形地图：返回有效坐标
		validHexCoords = slf.getValidHexCoords()
	}

	// 构建地图数据（包含已挖掘信息）
	var mapData interface{}
	if slf.mineMap != nil {
		mapData = slf.buildMapDataForClient()
	}

	// 返回桌面信息
	var resp = &response.EnterRoom{
		RoomID:         slf.RoomID,
		RoomType:       slf.RoomType,
		PlayerNum:      slf.playerNum,
		MapType:        slf.MapType,
		Fee:            slf.fee,
		Users:          users,
		GameStatus:     slf.gameStatus,
		CountDown:      slf.countDown,
		ValidHexCoords: validHexCoords,
		MapData:        mapData,          // 添加地图数据
		IsOnlineMode:   !slf.IsLevelMode, // 判断是否为联机模式
	}

	// 联机模式：添加首选玩家信息
	if !slf.IsLevelMode {
		resp.FirstPlayer = slf.firstPlayerInRound
	}

	logx.Infof("用户断线重连成功 RoomID:%v userID:%v coin:%v", slf.RoomID, user.UserID, roomUser.Coin)
	user.SendMessage(constvar.MsgTypeEnterRoom, ecode.OK, resp)

	// 前端断线重连需要
	time.Sleep(time.Second)
}

// OnUserFirstMoveEnd 玩家先手动画结束
func (slf *Room) OnUserFirstMoveEnd(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusFirstMove {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if slf.isFirstMoveEnd {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd IsFirstMoveEnd:%v", slf.RoomID, user.UserID, slf.isFirstMoveEnd)
		return
	}
	slf.isFirstMoveEnd = true

	logx.Infof("用户先手结束成功 RoomID:%v userID:%v", slf.RoomID, user.UserID)
	user.SendMessage(constvar.MsgTypeFirstMoveEnd, ecode.OK, struct{}{})

	slf.timerTask.Add(500, func() {
		slf.ProcessCheckFirstMove(true)
	})
}

// OnUserRollDice 玩家掷骰子请求
func (slf *Room) OnUserRollDice(msg *request.PackMessage) {
	params := &request.RollDice{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd {
		logx.Infof("RoomID:%v userID:%v can not rollDice, gameStatus:%v, curTokenPos:%v, IsRollDiceEnd:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserRollDice(roomUser.Pos, roomUser.UserID, OpByClient, params.DicePoint)
}

// OnUserMoveChessEnd 玩家移动棋子结束请求
func (slf *Room) OnUserMoveChessEnd(msg *request.PackMessage) {
	params := &request.MoveChessEnd{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	if slf.gameStatus != constvar.GameStatusMoveChess {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if params.GameRollTimes != slf.rollTimes {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, rollTimes:%v, gameRollTimes:%v", slf.RoomID, user.UserID, slf.rollTimes, params.GameRollTimes)
		return
	}

	// 防止前端异常，限制最小移动棋子的时间(最小1秒)
	const moveChessTime = 5 // 移动棋子时间5秒
	if moveChessTime-slf.countDown < 1 {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, moveChessTime:%v, countDown:%v", slf.RoomID, user.UserID, moveChessTime, slf.countDown)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	user.SendMessage(constvar.MsgTypeMoveChessEnd, ecode.OK, struct{}{})

	slf.ProcessCheckMoveChess(true)
}

// OnUserChoiceAdvance 玩家选择前进点数
func (slf *Room) OnUserChoiceAdvance(msg *request.PackMessage) {
	params := &request.ChoiceAdvance{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	// 只能选择1-3点数
	if params.DicePoint <= 0 || params.DicePoint >= 4 {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusUseProp ||
		slf.curTokenPos != roomUser.Pos ||
		!slf.allPlayerInfo[roomUser.Pos].IsUsingProp(constvar.GamePropAdvancement) {
		logx.Infof("RoomID:%v userID:%v can not choiceAdvance, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceAdvance(roomUser.Pos, roomUser.UserID, params.DicePoint, OpByClient)
}

// OnUserChoiceProp 玩家挑选道具请求
func (slf *Room) OnUserChoiceProp(msg *request.PackMessage) {
	params := &request.ChoiceProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusChoiceProp ||
		slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not choiceProp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	if slf.allPlayerInfo[roomUser.Pos].OwnProp > 0 {
		logx.Infof("RoomID:%v userID:%v already have prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrAlreadyHaveProp, struct{}{})
		return
	}

	curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[roomUser.Pos].CurChessPos)
	if curBlock == nil {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no find block", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos)
		return
	}
	if !curBlock.isHaveProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrBlockNoSuchProp, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnUserUseProp 玩家使用道具请求
func (slf *Room) OnUserUseProp(msg *request.PackMessage) {
	params := &request.UseProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	if !params.PropType.Valid() {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].RollTimes > 0 {
		logx.Infof("RoomID:%v userID:%v can not use prop, gameStatus:%v, curTokenPos:%v, rollTimes:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].RollTimes)
		return
	}

	// 是否拥有该道具
	if slf.allPlayerInfo[roomUser.Pos].OwnProp != params.PropType {
		logx.Infof("RoomID:%v userID:%v no prop:%v, ownProp:%v", slf.RoomID, user.UserID, params.PropType, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrPropNotFound, struct{}{})
		return
	}

	// 该种道具是否在使用中
	if slf.allPlayerInfo[roomUser.Pos].IsUsingProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v prop:%v is using", slf.RoomID, user.UserID, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrPropUsing, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserUseProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnReqSmartOp 请求智能操作
func (slf *Room) OnReqSmartOp(msg *request.PackMessage) {
	// 判断房间是否已结算
	if slf.gameStatus <= 0 {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not reqSmartOp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	slf.ProcessUserSmartOp(roomUser.Pos, msg.Ext)
}

// OnForceCloseGame 强制关闭游戏
func (slf *Room) OnForceCloseGame() {
	logx.Infof("RoomID:%v OnForceCloseGame IsLevelMode:%v", slf.RoomID, slf.IsLevelMode)

	if slf.IsLevelMode {
		// 关卡模式：推送游戏结束通知后直接强制关闭，不进行联机结算
		slf.sendLevelForceCloseNotification()
		slf.forceCloseLevelRoom()
		return
	}

	// 联机模式：直接强制关闭，不进行结算（因为其他玩家还在正常游戏）
	slf.ForceCloseRoom()
}

// OnUserForceLeave 强制玩家离开
func (slf *Room) OnUserForceLeave(msg *request.PackMessage) {
	params := &request.UserLeave{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	roomUser := slf.GetUser(params.UserId)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, params.UserId)
		return
	}

	logx.Infof("用户强制离开房间成功 RoomID:%v userID:%v", slf.RoomID, roomUser.UserID)
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, roomUser.UserID, slf.RoomID)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: roomUser.UserID,
	})
	// 必须放最后，否则广播不到自己
	roomUser.IsLeave = true
}

// OnUserClickBlock 处理用户点击方块操作
func (slf *Room) OnUserClickBlock(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("用户不存在 RoomID:%v userID:%v", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 检查并退出AI托管（玩家主动操作时退出托管）
	if slf.aiManager != nil && slf.aiManager.IsUserAIManaged(user.UserID) {
		err := slf.aiManager.StopAIManagement(user.UserID)
		if err != nil {
			logx.Errorf("退出AI托管失败 UserID:%s Error:%v", user.UserID, err)
		} else {
			logx.Infof("玩家主动操作，退出AI托管 UserID:%s RoomID:%d", user.UserID, slf.RoomID)
		}
	}

	// 检查房间用户（使用标准的GetUser方法）
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Errorf("房间用户不存在 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 检查游戏状态
	if slf.gameStatus != constvar.GameStatusMinesweeping {
		logx.Errorf("游戏状态错误，无法点击方块 RoomID:%v userID:%v gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	// 检查地图类型（现在支持方格和六边形地图）
	if slf.MapType != constvar.MapTypeGrid && slf.MapType != constvar.MapTypeHexagon {
		logx.Errorf("地图类型错误，不支持的扫雷地图类型 RoomID:%v userID:%v mapType:%v", slf.RoomID, user.UserID, slf.MapType)
		return
	}

	// 解析请求参数（支持方格坐标和六边形坐标）
	var req struct {
		X      int `json:"x"` // 方格地图坐标
		Y      int `json:"y"` // 方格地图坐标
		Q      int `json:"q"` // 六边形地图坐标
		R      int `json:"r"` // 六边形地图坐标
		Action int `json:"action"`
	}
	if err := mapstructure.Decode(msg.Data, &req); err != nil {
		logx.Errorf("解析点击方块请求失败 RoomID:%v userID:%v err:%v", slf.RoomID, user.UserID, err)
		return
	}

	// 根据地图类型处理坐标
	var finalX, finalY int
	if slf.MapType == constvar.MapTypeHexagon {
		// 六边形地图：使用q,r坐标
		finalX, finalY = req.Q, req.R
		logx.Infof("六边形地图坐标 RoomID:%v userID:%v q:%v r:%v", slf.RoomID, user.UserID, req.Q, req.R)
	} else {
		// 方格地图：使用x,y坐标
		finalX, finalY = req.X, req.Y
		logx.Infof("方格地图坐标 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, req.X, req.Y)
	}

	// 验证坐标有效性
	if !slf.mineMap.IsValidPosition(finalX, finalY) {
		logx.Errorf("坐标无效 RoomID:%v userID:%v x:%v y:%v mapType:%v", slf.RoomID, user.UserID, finalX, finalY, slf.MapType)
		return
	}

	// 验证操作类型
	if req.Action != 1 && req.Action != 2 {
		logx.Errorf("操作类型无效 RoomID:%v userID:%v action:%v", slf.RoomID, user.UserID, req.Action)
		return
	}

	// 检查是否在允许操作的时间内（前20秒允许操作，后5秒只展示）
	// countDown从25倒计时到0，当countDown <= 5时表示进入展示阶段
	if slf.countDown <= MinesweeperShowTime {
		logx.Infof("操作时间已结束，当前为展示阶段 RoomID:%v userID:%v countDown:%v",
			slf.RoomID, user.UserID, slf.countDown)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "操作时间已结束")
		return
	}

	// 获取目标方块并验证状态
	block := slf.mineMap.GetBlock(finalX, finalY)
	if block == nil {
		logx.Errorf("获取方块失败 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, finalX, finalY)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "方块不存在")
		return
	}

	// 检查方块是否可以进行操作（与机器人逻辑保持一致）
	// 1. 检查方块是否已被揭示（挖掘过）
	if block.IsRevealed {
		logx.Infof("方块已被揭示，拒绝操作 RoomID:%v userID:%v action:%v x:%v y:%v",
			slf.RoomID, user.UserID, req.Action, finalX, finalY)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "该方块已被挖掘，无法再次操作")
		return
	}

	// 2. 检查方块是否已被标记
	if block.IsMarked {
		logx.Infof("方块已被标记，拒绝操作 RoomID:%v userID:%v action:%v x:%v y:%v",
			slf.RoomID, user.UserID, req.Action, finalX, finalY)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "该方块已被标记，无法再次操作")
		return
	}

	// 3. 检查方块是否在以前回合被操作过
	// block.Players 在操作阶段只包含以前回合的玩家
	hasHistoryOperation := len(block.Players) > 0
	if hasHistoryOperation {
		logx.Infof("方块在以前回合已被操作，拒绝操作 RoomID:%v userID:%v action:%v x:%v y:%v Players:%v",
			slf.RoomID, user.UserID, req.Action, finalX, finalY, block.Players)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "该方块在以前回合已被操作过")
		return
	}

	// 同一回合内允许所有操作：
	// - 玩家可以覆盖自己的操作（换位置或改操作类型）
	// - 多个玩家可以操作同一方块（给予加分或减分）
	// - 不限制已挖掘方块的标记等操作

	// 记录首选玩家（只记录第一个发送请求的玩家）
	if slf.firstPlayerInRound == "" {
		slf.firstPlayerInRound = user.UserID
		logx.Infof("首选玩家记录 RoomID:%v Round:%d FirstPlayer:%v",
			slf.RoomID, slf.currentRound, user.UserID)
	}

	// 存储玩家操作（覆盖之前的操作）
	action := &RoundAction{
		UserID:    user.UserID,
		X:         finalX,
		Y:         finalY,
		Action:    req.Action,
		Timestamp: time.Now().Unix(),
		Score:     0, // 回合结束时计算
	}
	slf.roundActions[user.UserID] = action

	// 如果是首选玩家，立即推送首选玩家奖励
	slf.pushFirstChoiceBonusIfApplicable(user.UserID)

	// 响应客户端
	user.SendMessage(msg.MsgID, ecode.OK, map[string]interface{}{})

	// 检查是否所有玩家都已操作完毕，如果是则立即进入展示阶段
	slf.checkAllPlayersOperatedAndTriggerDisplay()

	// 保存联机游戏状态
	slf.saveOnlineGameState()

	logx.Infof("用户点击方块操作成功 RoomID:%v userID:%v x:%v y:%v action:%v",
		slf.RoomID, user.UserID, finalX, finalY, req.Action)
}

// ==================== 单机关卡系统处理函数 ====================

// OnLevelClickBlock 处理关卡模式点击方块操作
// 注意：现在通过统一的房间消息路径调用，与联机模式保持架构一致
func (slf *Room) OnLevelClickBlock(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("用户不存在 RoomID:%v userID:%v", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 验证是否为关卡房间
	if !slf.IsLevelMode {
		logx.Errorf("非关卡房间，拒绝关卡点击操作 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "非关卡房间")
		return
	}

	// 验证用户权限
	if slf.LevelUserID != user.UserID {
		logx.Errorf("无权限操作此关卡房间 RoomID:%v userID:%v levelUserID:%v", slf.RoomID, user.UserID, slf.LevelUserID)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "无权限操作此房间")
		return
	}

	// 检查游戏状态
	if slf.gameStatus != constvar.GameStatusMinesweeping {
		logx.Errorf("关卡游戏状态错误 RoomID:%v userID:%v gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "游戏未开始或已结束")
		return
	}

	// 检查游戏是否已结束
	if slf.IsGameEnded {
		logx.Errorf("关卡游戏已结束 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "游戏已结束")
		return
	}

	// 解析请求参数
	var req struct {
		X      int `json:"x"`
		Y      int `json:"y"`
		Q      int `json:"q"`
		R      int `json:"r"`
		Action int `json:"action"`
	}
	if err := mapstructure.Decode(msg.Data, &req); err != nil {
		logx.Errorf("解析关卡点击请求失败 RoomID:%v userID:%v err:%v", slf.RoomID, user.UserID, err)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "参数格式错误")
		return
	}

	// 验证操作类型
	if req.Action != 1 && req.Action != 2 {
		logx.Errorf("关卡操作类型无效 RoomID:%v userID:%v action:%v", slf.RoomID, user.UserID, req.Action)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "操作类型无效")
		return
	}

	// 坐标处理
	var finalX, finalY int
	if slf.MapType == constvar.MapTypeHexagon {
		finalX, finalY = req.Q, req.R
		// 兼容处理：如果六边形坐标为0但方格坐标不为0，使用方格坐标
		if finalX == 0 && finalY == 0 && req.X != 0 && req.Y != 0 {
			finalX, finalY = req.X, req.Y
		}
	} else {
		finalX, finalY = req.X, req.Y
	}

	// 验证坐标
	if !slf.mineMap.IsValidPosition(finalX, finalY) {
		logx.Errorf("关卡坐标无效 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, finalX, finalY)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "坐标无效")
		return
	}

	// 获取目标方块
	block := slf.mineMap.GetBlock(finalX, finalY)
	if block == nil {
		logx.Errorf("关卡获取方块失败 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, finalX, finalY)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "方块不存在")
		return
	}

	// 关卡模式的方块状态检查
	if req.Action == 1 { // 挖掘操作
		if block.IsRevealed {
			user.SendMessage(msg.MsgID, ecode.ErrParams, "该方块已被挖掘")
			return
		}
		if block.IsMarked {
			user.SendMessage(msg.MsgID, ecode.ErrParams, "该方块已被标记，请先取消标记")
			return
		}
	} else if req.Action == 2 { // 标记操作
		if block.IsRevealed {
			user.SendMessage(msg.MsgID, ecode.ErrParams, "该方块已被挖掘，无法标记")
			return
		}
		// 标记操作允许在已标记和未标记之间切换
	}

	// 执行关卡操作
	result := slf.processLevelBlockActionOnly(finalX, finalY, req.Action, user.UserID)

	// 检查游戏结束条件
	gameStatus := slf.checkLevelGameEndConditions()

	// 构建关卡模式专用响应（移除gameStatus字段）
	response := map[string]interface{}{
		"success":        true,
		"x":              finalX,
		"y":              finalY,
		"action":         req.Action,
		"result":         result.Result,
		"remainingMines": slf.getRemainingMines(),
		"message":        result.Message,
	}

	// 添加连锁展开结果
	if result.FloodFillResult != nil && len(result.FloodFillResult.RevealedBlocks) > 0 {
		floodFillResults := make([]map[string]interface{}, len(result.FloodFillResult.RevealedBlocks))
		for i, revealed := range result.FloodFillResult.RevealedBlocks {
			floodFillResults[i] = map[string]interface{}{
				"x":             revealed.X,
				"y":             revealed.Y,
				"neighborMines": revealed.NeighborMines,
			}
		}
		response["floodFillResults"] = floodFillResults
	}

	// 发送响应
	user.SendMessage(msg.MsgID, ecode.OK, response)

	// 检查游戏是否结束，如果结束则自动处理
	if gameStatus == constvar.GameStatusLevelSuccess || gameStatus == constvar.GameStatusLevelFailed {
		slf.handleLevelGameEnd(user)
	} else {
		// 游戏未结束，保存状态到Redis
		slf.saveLevelGameState()
	}

	logx.Infof("关卡点击操作成功 RoomID:%v userID:%v x:%v y:%v action:%v result:%v gameStatus:%v",
		slf.RoomID, user.UserID, finalX, finalY, req.Action, result.Result, gameStatus)
}

// OnLevelProgress 获取关卡进度
func (slf *Room) OnLevelProgress(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 获取用户关卡进度
	progressSearch := model.NewUserLevelProgressSearch()
	totalLevels, clearedLevels, currentLevel, err := progressSearch.GetUserProgress(
		msg.Ext.AppChannel, msg.Ext.AppID, user.UserID)

	if err != nil {
		logx.Errorf("获取用户关卡进度失败 UserID:%s Error:%v", user.UserID, err)
		user.SendMessage(msg.MsgID, ecode.ErrSave, struct{}{})
		return
	}

	// 构建进度响应（与API文档保持一致）
	response := map[string]interface{}{
		"totalLevels":   totalLevels,
		"clearedLevels": clearedLevels,
		"currentLevel":  currentLevel,
	}

	user.SendMessage(msg.MsgID, ecode.OK, response)
	logx.Infof("获取关卡进度成功 UserID:%s 总关卡:%d 已完成:%d 当前关卡:%d",
		user.UserID, totalLevels, clearedLevels, currentLevel)
}

// ==================== 关卡模式点击方块处理 ====================

// LevelActionResult 关卡操作结果
type LevelActionResult struct {
	Result          interface{}      `json:"result"`
	Message         string           `json:"message"`
	FloodFillResult *FloodFillResult `json:"floodFillResult,omitempty"`
}

// processLevelBlockActionOnly 处理关卡方块操作（独立实现）
func (slf *Room) processLevelBlockActionOnly(x, y, action int, userID string) *LevelActionResult {
	block := slf.mineMap.GetBlock(x, y)
	result := &LevelActionResult{}

	if action == 1 { // 挖掘操作
		if block.IsMine {
			// 踩雷
			block.IsRevealed = true
			slf.IsGameEnded = true
			slf.GameResult = false
			result.Result = "mine"
			result.Message = "踩雷了！游戏失败"
		} else {
			// 安全方块
			block.IsRevealed = true
			slf.mineMap.RevealedCount++
			result.Result = block.NeighborMines

			if block.NeighborMines == 0 {
				// 空白方块，触发连锁展开
				floodResult := slf.mineMap.ProcessFloodFill(x, y, userID)
				result.FloodFillResult = floodResult
				result.Message = "挖掘成功，触发连锁展开"
			} else {
				result.Message = fmt.Sprintf("挖掘成功，周围有%d颗地雷", block.NeighborMines)
			}
		}
	} else if action == 2 { // 标记操作
		// 实现标记状态切换
		block.IsMarked = !block.IsMarked
		if block.IsMarked {
			// 标记操作不透露是否为地雷，保持游戏悬念
			result.Result = "marked"
			result.Message = "标记成功"
		} else {
			result.Result = "unmarked"
			result.Message = "取消标记"
		}
	}

	return result
}

// checkLevelGameEndConditions 检查关卡游戏结束条件（使用统一状态值）
func (slf *Room) checkLevelGameEndConditions() constvar.GameStatus {
	if slf.IsGameEnded {
		if slf.GameResult {
			return constvar.GameStatusLevelSuccess // 胜利
		} else {
			return constvar.GameStatusLevelFailed // 失败
		}
	}

	// 失败条件：检查是否踩雷
	if slf.checkMineExploded() {
		slf.IsGameEnded = true
		slf.GameResult = false
		return constvar.GameStatusLevelFailed // 失败
	}

	// 胜利条件1：所有非地雷方块都被挖掘（传统扫雷胜利条件）
	totalBlocks := slf.getTotalBlocks()
	if slf.mineMap.RevealedCount == totalBlocks-slf.mineMap.MineCount {
		slf.IsGameEnded = true
		slf.GameResult = true
		return constvar.GameStatusLevelSuccess // 胜利
	}

	// 胜利条件2：所有地雷都被正确标记（严格模式）
	if slf.checkAllMinesCorrectlyMarked() {
		slf.IsGameEnded = true
		slf.GameResult = true
		return constvar.GameStatusLevelSuccess // 胜利
	}

	return constvar.GameStatusMinesweeping // 进行中
}

// checkAllMinesCorrectlyMarked 检查所有地雷是否都被正确标记
func (slf *Room) checkAllMinesCorrectlyMarked() bool {
	if slf.mineMap == nil {
		return false
	}

	correctlyMarkedMines := 0
	incorrectlyMarkedBlocks := 0

	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range slf.mineMap.HexBlocks {
			if block == nil {
				continue
			}
			if block.IsMarked {
				if block.IsMine {
					correctlyMarkedMines++
				} else {
					incorrectlyMarkedBlocks++
				}
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block == nil {
					continue
				}
				if block.IsMarked {
					if block.IsMine {
						correctlyMarkedMines++
					} else {
						incorrectlyMarkedBlocks++
					}
				}
			}
		}
	}

	// 胜利条件：所有地雷都被正确标记，且没有错误标记
	allMinesMarked := (correctlyMarkedMines == slf.mineMap.MineCount)
	noIncorrectMarks := (incorrectlyMarkedBlocks == 0)

	logx.Infof("地雷标记检查 RoomID:%v TotalMines:%d CorrectlyMarked:%d IncorrectMarks:%d",
		slf.RoomID, slf.mineMap.MineCount, correctlyMarkedMines, incorrectlyMarkedBlocks)

	return allMinesMarked && noIncorrectMarks
}

// checkMineExploded 检查是否有地雷被挖掘（踩雷）
func (slf *Room) checkMineExploded() bool {
	if slf.mineMap == nil {
		return false
	}

	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range slf.mineMap.HexBlocks {
			if block == nil {
				continue
			}
			// 如果地雷被挖掘，说明踩雷了
			if block.IsMine && block.IsRevealed {
				logx.Infof("踩雷检测 RoomID:%v 地雷位置:(%d,%d)", slf.RoomID, block.X, block.Y)
				return true
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block == nil {
					continue
				}
				// 如果地雷被挖掘，说明踩雷了
				if block.IsMine && block.IsRevealed {
					logx.Infof("踩雷检测 RoomID:%v 地雷位置:(%d,%d)", slf.RoomID, x, y)
					return true
				}
			}
		}
	}

	return false
}

// getTotalBlocks 获取地图总方块数
func (slf *Room) getTotalBlocks() int {
	if slf.MapType == constvar.MapTypeGrid {
		return slf.mineMap.Width * slf.mineMap.Height
	} else {
		return len(slf.mineMap.ValidHexes)
	}
}

// handleLevelGameEnd 处理关卡游戏自然结束（胜利/失败）
func (slf *Room) handleLevelGameEnd(user *usermgr.User) {
	if !slf.IsLevelMode || !slf.IsGameEnded {
		return
	}

	// 构建游戏结束通知
	response := map[string]interface{}{
		"levelId":   slf.LevelID,
		"isSuccess": slf.GameResult,
	}

	// 根据游戏结果添加不同信息
	if slf.GameResult {
		// 胜利
		response["message"] = fmt.Sprintf("恭喜通关第%d关！", slf.LevelID)

		// 自动更新用户进度
		slf.autoUpdateUserProgress(user)

		// 添加下一关信息
		if slf.LevelID < 30 {
			response["nextLevelId"] = slf.LevelID + 1
		}
	} else {
		// 失败
		response["message"] = fmt.Sprintf("第%d关挑战失败，请再试一次！", slf.LevelID)
	}

	// 推送游戏结束通知
	user.SendMessage(constvar.MsgTypeLevelGameEnd, ecode.OK, response)

	// 清理Redis状态
	GetInstance().clearLevelGameState(slf.appChannel, slf.appID, user.UserID)

	// 清理用户的本地房间信息
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, user.UserID, slf.RoomID)

	// 延迟删除房间（给客户端时间处理消息）
	GetInstance().AddRmvRoomId(slf.RoomID, time.Now().Add(5*time.Second).UnixMilli())

	logx.Infof("关卡游戏自然结束 UserID:%s LevelID:%d RoomID:%d IsSuccess:%v",
		user.UserID, slf.LevelID, slf.RoomID, slf.GameResult)
}

// sendLevelForceCloseNotification 发送关卡强制关闭通知
func (slf *Room) sendLevelForceCloseNotification() {
	if !slf.IsLevelMode || slf.LevelUserID == "" {
		return
	}

	user := usermgr.GetInstance().GetUserById(slf.appChannel, slf.appID, slf.LevelUserID)
	if user == nil {
		logx.Warnf("关卡强制关闭时用户不存在 RoomID:%v UserID:%s", slf.RoomID, slf.LevelUserID)
		return
	}

	// 构建强制关闭通知
	response := map[string]interface{}{
		"levelId":   slf.LevelID,
		"isSuccess": false, // 强制关闭视为失败
		"message":   "游戏被强制关闭",
		"reason":    "系统维护或管理员操作",
	}

	// 推送游戏结束通知
	user.SendMessage(constvar.MsgTypeLevelGameEnd, ecode.OK, response)

	logx.Infof("关卡强制关闭通知已发送 UserID:%s LevelID:%d RoomID:%d",
		slf.LevelUserID, slf.LevelID, slf.RoomID)
}

// getValidHexCoords 获取有效的六边形坐标
func (slf *Room) getValidHexCoords() []*response.HexCoord {
	if slf.IsLevelMode {
		// 关卡模式：根据关卡配置获取六边形坐标
		return slf.getLevelHexCoords()
	} else {
		// 联机模式：使用预定义的六边形地图配置
		return slf.getOnlineHexCoords()
	}
}

// getLevelHexCoords 获取关卡模式的六边形坐标
func (slf *Room) getLevelHexCoords() []*response.HexCoord {
	if slf.mineMap == nil || len(slf.mineMap.ValidHexes) == 0 {
		logx.Warnf("关卡模式六边形地图数据为空 RoomID:%v LevelID:%d", slf.RoomID, slf.LevelID)
		return nil
	}

	// 将内部坐标转换为响应格式
	coords := make([]*response.HexCoord, len(slf.mineMap.ValidHexes))
	for i, hex := range slf.mineMap.ValidHexes {
		coords[i] = &response.HexCoord{
			Q: hex.Q,
			R: hex.R,
		}
	}

	logx.Infof("关卡模式六边形坐标 RoomID:%v LevelID:%d HexCount:%d",
		slf.RoomID, slf.LevelID, len(coords))
	return coords
}

// getOnlineHexCoords 获取联机模式的六边形坐标
func (slf *Room) getOnlineHexCoords() []*response.HexCoord {
	// 联机模式使用预定义的六边形地图配置（MapID: 0）
	config := getHexMapConfigByID(0)
	if config == nil {
		logx.Errorf("联机模式获取六边形地图配置失败 RoomID:%v", slf.RoomID)
		return nil
	}

	// 将配置坐标转换为响应格式
	coords := make([]*response.HexCoord, len(config.ValidHexes))
	for i, hex := range config.ValidHexes {
		coords[i] = &response.HexCoord{
			Q: hex.Q,
			R: hex.R,
		}
	}

	logx.Infof("联机模式六边形坐标 RoomID:%v MapID:0 HexCount:%d",
		slf.RoomID, len(coords))
	return coords
}

// hasGameProgress 检查关卡模式游戏是否有进度（用于断线重连判断）
func (slf *Room) hasGameProgress() bool {
	// 此方法专门用于关卡模式的断线重连判断
	// 关卡模式逻辑：只要游戏已经开始（房间存在且未结束），就认为是断线重连
	// 这包括玩家还没有点击任何方块的情况
	return !slf.IsGameEnded
}

// calculatePlayerGameScore 计算玩家当前游戏得分（联机模式）
func (slf *Room) calculatePlayerGameScore(userID string) int {
	if slf.playerTotalScores == nil {
		return 0
	}

	score, exists := slf.playerTotalScores[userID]
	if !exists {
		return 0
	}

	return score
}

// isPlayerAlive 检查玩家是否还在游戏中（联机模式）
func (slf *Room) isPlayerAlive(userID string) bool {
	// isAlive字段的含义：玩家是否主动退出房间
	// 区分两种情况：
	// 1. 玩家断线（IsOffline = true）：仍然在游戏中，isAlive = true
	// 2. 玩家主动退出（IsLeave = true）：不在游戏中，isAlive = false

	roomUser := slf.GetUser(userID)
	if roomUser == nil {
		// 玩家不在房间中，可能是房间初始化时还没有该玩家
		// 这种情况下返回false是合理的
		return false
	}

	// 只检查玩家是否主动离开房间
	// 断线玩家（IsOffline = true）仍然被认为在游戏中
	return !roomUser.IsLeave
}

// getMarkedMineCount 获取已标记的地雷数量
func (slf *Room) getMarkedMineCount() int {
	if slf.mineMap == nil {
		return 0
	}

	count := 0
	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图
		for _, block := range slf.mineMap.HexBlocks {
			if block != nil && block.IsMarked {
				count++
			}
		}
	default:
		// 方格地图
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block != nil && block.IsMarked {
					count++
				}
			}
		}
	}

	return count
}

// getRevealedBlocksForClient 获取已挖掘方块的客户端数据
func (slf *Room) getRevealedBlocksForClient() []map[string]interface{} {
	if slf.mineMap == nil {
		return nil
	}

	revealed := make([]map[string]interface{}, 0)

	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图
		for _, hex := range slf.mineMap.ValidHexes {
			coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
			block := slf.mineMap.HexBlocks[coordKey]
			if block != nil && block.IsRevealed {
				revealed = append(revealed, map[string]interface{}{
					"q":             hex.Q,
					"r":             hex.R,
					"neighborMines": block.NeighborMines,
				})
			}
		}
	default:
		// 方格地图
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block != nil && block.IsRevealed {
					revealed = append(revealed, map[string]interface{}{
						"x":             x,
						"y":             y,
						"neighborMines": block.NeighborMines,
					})
				}
			}
		}
	}

	return revealed
}

// getMarkedBlocksForClient 获取已标记方块的客户端数据
func (slf *Room) getMarkedBlocksForClient() []map[string]interface{} {
	if slf.mineMap == nil {
		return nil
	}

	marked := make([]map[string]interface{}, 0)

	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图
		for _, hex := range slf.mineMap.ValidHexes {
			coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
			block := slf.mineMap.HexBlocks[coordKey]
			if block != nil && block.IsMarked {
				marked = append(marked, map[string]interface{}{
					"q": hex.Q,
					"r": hex.R,
				})
			}
		}
	default:
		// 方格地图
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block != nil && block.IsMarked {
					marked = append(marked, map[string]interface{}{
						"x": x,
						"y": y,
					})
				}
			}
		}
	}

	return marked
}

// autoUpdateUserProgress 自动更新用户关卡进度（仅胜利时调用）
func (slf *Room) autoUpdateUserProgress(user *usermgr.User) {
	if !slf.GameResult {
		return // 只有胜利时才更新进度
	}

	// 获取用户当前进度
	progressSearch := model.NewUserLevelProgressSearch()
	_, _, currentLevel, err := progressSearch.GetUserProgress(slf.appChannel, slf.appID, user.UserID)
	if err != nil {
		logx.Errorf("获取用户关卡进度失败 UserID:%s Error:%v", user.UserID, err)
		return
	}

	// 检查是否需要更新进度
	if slf.LevelID >= currentLevel {
		// 更新用户进度
		err = progressSearch.CreateOrUpdate(slf.appChannel, slf.appID, user.UserID, slf.LevelID)
		if err != nil {
			logx.Errorf("自动更新用户关卡进度失败 UserID:%s LevelID:%d Error:%v",
				user.UserID, slf.LevelID, err)
		} else {
			logx.Infof("自动更新用户关卡进度成功 UserID:%s LevelID:%d",
				user.UserID, slf.LevelID)
		}
	}
}

// OnCancelAIManagement 处理取消AI托管请求
func (slf *Room) OnCancelAIManagement(msg *request.PackMessage) {
	// 获取用户信息
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("取消AI托管失败，用户不存在 RoomID:%v UserID:%v", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 检查用户是否在房间中
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Errorf("取消AI托管失败，用户不在房间中 RoomID:%v UserID:%v", slf.RoomID, user.UserID)
		return
	}

	// 检查是否为关卡模式（关卡模式不支持AI托管）
	if slf.IsLevelMode {
		logx.Warnf("关卡模式不支持AI托管操作 RoomID:%v UserID:%v", slf.RoomID, user.UserID)
		return
	}

	// 检查AI管理器是否存在
	if slf.aiManager == nil {
		logx.Errorf("AI管理器未初始化 RoomID:%v UserID:%v", slf.RoomID, user.UserID)
		return
	}

	// 尝试取消AI托管
	err := slf.aiManager.StopAIManagement(user.UserID)
	if err != nil {
		// 用户不在托管中，记录信息但不报错（这是正常情况）
		logx.Infof("取消AI托管：%v RoomID:%v UserID:%v", err.Error(), slf.RoomID, user.UserID)
		return
	}

	// 更新最后消息时间
	slf.UpdateLastMsg(roomUser)

	logx.Infof("用户主动取消AI托管成功 RoomID:%v UserID:%v", slf.RoomID, user.UserID)

	// 注意：状态变化通知已经在 aiManager.StopAIManagement() 中通过 AIStatusChange 消息广播
	// 此接口本身不返回任何响应数据，符合需求要求
}

// ==================== 数据构建优化方法 ====================

// buildUsersResponse 构建用户响应数据（优化版本）
func (slf *Room) buildUsersResponse(requestUserID string) []*response.RoomUser {
	// 预构建共享数据（避免重复计算）
	sharedData := slf.buildSharedDisplayData()

	var users []*response.RoomUser
	for _, v := range slf.allUser {
		if v.Pos < 0 {
			continue
		}

		// 构建用户响应
		roomUser := slf.buildSingleUserResponse(v, requestUserID, sharedData)
		users = append(users, roomUser)
	}

	return users
}

// SharedDisplayData 共享的展示数据（避免重复计算）
type SharedDisplayData struct {
	FloodFillResults []*response.FloodFillResult
	PlayerScores     map[string]int
	CurrentRound     int
}

// buildSharedDisplayData 构建共享的展示数据
func (slf *Room) buildSharedDisplayData() *SharedDisplayData {
	// 只在展示阶段且为联机模式时构建共享数据
	if slf.IsLevelMode || slf.gameStatus != constvar.GameStatusDisplay {
		return nil
	}

	// 构建连锁展开结果（所有玩家共享）
	floodFillResults := make([]*response.FloodFillResult, 0, len(slf.floodFillResults))
	for _, result := range slf.floodFillResults {
		floodResult := &response.FloodFillResult{
			TriggerX: result.TriggerX,
			TriggerY: result.TriggerY,
		}

		// 构建展开的方块列表
		for _, block := range result.RevealedBlocks {
			floodResult.RevealedCells = append(floodResult.RevealedCells, struct {
				X             int `json:"x"`
				Y             int `json:"y"`
				NeighborMines int `json:"neighborMines"`
			}{
				X:             block.X,
				Y:             block.Y,
				NeighborMines: block.NeighborMines,
			})
		}
		floodFillResults = append(floodFillResults, floodResult)
	}

	// 构建玩家回合得分（所有玩家共享）
	playerScores := make(map[string]int)
	for userID, action := range slf.roundActions {
		playerScores[userID] = action.Score
	}

	return &SharedDisplayData{
		FloodFillResults: floodFillResults,
		PlayerScores:     playerScores,
		CurrentRound:     slf.currentRound,
	}
}

// buildSingleUserResponse 构建单个用户的响应数据
func (slf *Room) buildSingleUserResponse(roomUser *RoomUser, requestUserID string, sharedData *SharedDisplayData) *response.RoomUser {
	// 构建基础用户信息
	user := &response.RoomUser{
		UserBaseInfo: response.UserBaseInfo{
			UserID:   roomUser.UserID,
			NickName: roomUser.NickName,
			Avatar:   roomUser.Avatar,
			Pos:      roomUser.Pos,
			Coin:     roomUser.Coin,
			Status:   roomUser.UserStatus,
		},
	}

	// 联机模式下添加游戏相关字段
	if !slf.IsLevelMode {
		// 填充游戏状态信息
		user.GameStateInfo = response.GameStateInfo{
			GameScore:   slf.calculatePlayerGameScore(roomUser.UserID),
			IsAlive:     slf.isPlayerAlive(roomUser.UserID),
			IsAIManaged: slf.getAIManagementStatus(roomUser.UserID),
		}

		// 根据游戏状态填充操作和展示信息
		slf.fillUserOperationInfo(user, roomUser, requestUserID)
		slf.fillUserDisplayInfo(user, roomUser, sharedData)
	}

	return user
}

// getAIManagementStatus 获取AI托管状态
func (slf *Room) getAIManagementStatus(userID string) bool {
	if slf.aiManager != nil {
		return slf.aiManager.IsUserAIManaged(userID)
	}
	return false
}

// fillUserOperationInfo 填充用户操作信息
func (slf *Room) fillUserOperationInfo(user *response.RoomUser, roomUser *RoomUser, requestUserID string) {
	if slf.gameStatus == constvar.GameStatusMinesweeping {
		// 等待阶段：只为断线重连的玩家本人添加操作记录（保持游戏公平性）
		if roomUser.UserID == requestUserID {
			if action, hasOperated := slf.roundActions[roomUser.UserID]; hasOperated {
				user.OperationInfo = response.OperationInfo{
					OperationX:    action.X,
					OperationY:    action.Y,
					OperationType: action.Action,
					IsFirstChoice: (roomUser.UserID == slf.firstPlayerInRound),
				}
			}
		}
	} else if slf.gameStatus == constvar.GameStatusDisplay {
		// 展示阶段：为所有玩家添加操作信息
		if action, hasOperated := slf.roundActions[roomUser.UserID]; hasOperated {
			user.OperationInfo = response.OperationInfo{
				OperationX:    action.X,
				OperationY:    action.Y,
				OperationType: action.Action,
				IsFirstChoice: (roomUser.UserID == slf.firstPlayerInRound),
			}
		} else {
			// 没有操作的玩家也要设置首选状态
			user.OperationInfo = response.OperationInfo{
				IsFirstChoice: (roomUser.UserID == slf.firstPlayerInRound),
			}
		}
	}
}

// fillUserDisplayInfo 填充用户展示信息
func (slf *Room) fillUserDisplayInfo(user *response.RoomUser, roomUser *RoomUser, sharedData *SharedDisplayData) {
	if slf.gameStatus == constvar.GameStatusDisplay && sharedData != nil {
		// 展示阶段：使用共享数据（避免重复计算）
		user.DisplayInfo = response.DisplayInfo{
			CurrentRound:     sharedData.CurrentRound,
			FloodFillResults: sharedData.FloodFillResults,
			PlayerScores:     sharedData.PlayerScores,
		}
	}
}
