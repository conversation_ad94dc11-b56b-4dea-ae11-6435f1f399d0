package roommgr

import (
	"context"
	"encoding/json"
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/redisx"
	"minesweep/common/safe"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model"
	"minesweep/model/common/request"
	"minesweep/usermgr"
	"sync"
	"time"

	"github.com/mitchellh/mapstructure"
)

type (
	RmvRoomEvent struct {
		RoomID   int64
		DoneTime int64 // 处理的时间
	}

	RoomManager struct {
		roomMap     sync.Map // roomID <---> *Room
		rmvRoomChan chan RmvRoomEvent
		stopCh      chan int
		stopWg      sync.WaitGroup
	}
)

var mgrInstance = &RoomManager{
	rmvRoomChan: make(chan RmvRoomEvent, 1000),
	stopCh:      make(chan int, 1),
}

func GetInstance() *RoomManager {
	return mgrInstance
}

func (r *RoomManager) CreateRoom(appChannel string, appID int64, playerNum int, fee int, roomType constvar.RoomType) *Room {
	room := NewRoom(appChannel, appID, playerNum, fee, roomType)
	if room != nil {
		r.roomMap.Store(room.RoomID, room)
	}
	return room
}

func (r *RoomManager) OnMsg(msg *request.PackMessage, roomID int64) int {
	value, ok := r.roomMap.Load(roomID)
	if !ok {
		logx.Infof("RoomManager OnMsg no find roomID:%v", roomID)
		return ecode.ErrNotFoundRoom
	}

	room := value.(*Room)
	room.OnMsg(msg)
	return ecode.OK
}

// UserOffline 房间内玩家-离线
func (r *RoomManager) UserOffline(roomID int64, appChannel string, appID int64, userID string) {
	value, ok := r.roomMap.Load(roomID)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeUserOffline,
			Ext:   request.ExtendInfo{AppChannel: appChannel, AppID: appID, UserID: userID},
		}
		room.OnMsg(msg)
	}
}

// GetRoom 获取房间
func (r *RoomManager) GetRoom(roomID int64) *Room {
	value, ok := r.roomMap.Load(roomID)
	if ok {
		return value.(*Room)
	}
	return nil
}

// GetChannelRooms 获取某渠道房间列表
func (r *RoomManager) GetChannelRooms(appChannel string, appID int64, propMode int) []*Room {
	var rooms []*Room
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil &&
			room.appChannel == appChannel &&
			room.appID == appID {
			// 扫雷游戏不需要 propMode 匹配
			rooms = append(rooms, room)
		}
		return true
	})
	return rooms
}

// RemoveRoom 立即删除房间
func (r *RoomManager) RemoveRoom(roomID int64) {
	logx.Infof("RoomManager RemoveRoom RoomID:%v begin", roomID)
	room := r.GetRoom(roomID)
	if room != nil {
		room.Stop()
		r.roomMap.Delete(roomID)
	}
	logx.Infof("房间管理器删除房间成功 RoomID:%v", roomID)
}

// AddRmvRoomId 添加延迟删除的房间
func (r *RoomManager) AddRmvRoomId(roomID int64, doneTime int64) {
	r.rmvRoomChan <- RmvRoomEvent{
		RoomID:   roomID,
		DoneTime: doneTime,
	}
}

// Start 启动房间管理器的主动逻辑
func (r *RoomManager) Start() {
	r.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer func() {
			logx.Info("RoomManager:Start 工作协程退出")
			ticker.Stop()
			r.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-r.stopCh:
				// 游戏中的所有房间强制结算
				r.forceCloseRooms()
				return
			case <-ticker.C:
				count++
				// 每分钟打印一次所有房间
				if count%60 == 0 {
					r.printRooms()
				}
				// 每分钟检测房间卡死
				if count%60 == 0 {
					r.checkBlock()
				}

				// 每分钟检测一次所有房间
				if count%60 == 0 {
					r.checkRooms()
				}
			}
		}
	})
}

// checkDelayRmvRooms 检查延迟回收的房间
func (r *RoomManager) checkDelayRmvRooms() {
	select {
	case rmvEvent := <-r.rmvRoomChan:
		if time.Now().UnixMilli() >= rmvEvent.DoneTime {
			room := r.GetRoom(rmvEvent.RoomID)
			if room == nil {
				return
			}

			room.Stop()
			r.roomMap.Delete(rmvEvent.RoomID)
		} else {
			r.rmvRoomChan <- rmvEvent
		}
	default:
		return
	}
}

func (r *RoomManager) Stop() {
	close(r.stopCh)
	r.stopWg.Wait()
}

// GetRoomCount 获取房间数量
func (r *RoomManager) GetRoomCount() int {
	var count int
	r.roomMap.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

// checkBlock 检查房间卡死
func (r *RoomManager) checkBlock() {
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			_ = room.CheckBlock()
		}
		return true
	})
}

// checkRooms 检查所有房间
func (r *RoomManager) checkRooms() {
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			_ = room.CheckRoom()
		}
		return true
	})
}

// GetUserRoomID 获取玩家所在的房间Id
func (r *RoomManager) GetUserRoomID(appChannel string, appID int64, userID string) int64 {
	var roomID int64
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room.appChannel != appChannel ||
			room.appID != appID {
			return true
		}

		// 玩家不在该房间，或者该玩家已经被禁止继续游戏
		user := room.GetUser(userID)
		if user == nil || user.IsLeave {
			return true
		}

		// 检查房间是否异常，如果异常，直接解散房间(删除了的位置)
		if !room.CheckRoom() {
			return true
		}

		roomID = room.RoomID
		return false
	})
	return roomID
}

// printRooms 打印所有房间
func (r *RoomManager) printRooms() {
	logx.Infof("===RoomManager printRooms roomCount:%v", r.GetRoomCount())
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			playerCount, robotCount, viewerCount := room.GetPlayerCount()
			logx.Infof("===RoomManager roomID:%v, playerCount:%v, robotCount:%v, viewerCount:%v", room.RoomID, playerCount, robotCount, viewerCount)
		}
		return true
	})
}

// GetPlayerCount 获取 所有房间的玩家数量和观众数量
func (r *RoomManager) GetPlayerCount() (int, int, int) {
	userCount := 0
	robotCount := 0
	viewerCount := 0
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			uc, rc, vc := room.GetPlayerCount()
			userCount += uc
			robotCount += rc
			viewerCount += vc
		}
		return true
	})
	return userCount, robotCount, viewerCount
}

// forceCloseRooms 强制关闭所有房间
func (r *RoomManager) forceCloseRooms() {
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			room.ForceCloseRoom()
		}
		return true
	})
}

// UserBuyProduct 房间内玩家-购买商品
func (r *RoomManager) UserBuyProduct(roomId int64, appChannel string, appID int64, userID string, params *request.BuyProduct) {
	value, ok := r.roomMap.Load(roomId)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeBuyProduct,
			Data:  params,
			Ext:   request.ExtendInfo{AppChannel: appChannel, AppID: appID, UserID: userID},
		}
		room.OnMsg(msg)
	}
}

// SetSkin 房间内玩家-设置皮肤
func (r *RoomManager) SetSkin(roomId int64, appChannel string, appID int64, userID string, params *request.SetSkin) {
	value, ok := r.roomMap.Load(roomId)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeSetSkin,
			Data:  params,
			Ext:   request.ExtendInfo{AppChannel: appChannel, AppID: appID, UserID: userID},
		}
		room.OnMsg(msg)
	}
}

// OnLevelMsg 处理关卡系统消息（仅处理不需要房间的消息）
func (r *RoomManager) OnLevelMsg(msg *request.PackMessage, req interface{}) int {
	// 直接调用关卡处理方法，不通过消息通道
	switch msg.MsgID {
	case constvar.MsgTypeLevelInfo: // "ExtendLevelInfo"
		// 扩展的关卡信息接口，支持开始游戏
		return r.OnExtendLevelInfo(msg)
	case constvar.MsgTypeLevelProgress:
		// 创建一个虚拟房间来处理关卡进度查询
		virtualRoom := &Room{
			RoomID: 0, // 虚拟房间ID
		}
		virtualRoom.OnLevelProgress(msg)
		return ecode.OK
	default:
		logx.Errorf("OnLevelMsg unknown msgID:%s", msg.MsgID)
		return ecode.ErrParams
	}
}

// 注意：OnLevelClickBlock 方法已删除
// 关卡点击方块消息现在通过正常房间路径处理：
// OnGameMsg → 房间查找 → room.OnLevelClickBlock

// OnExtendLevelInfo 扩展的关卡信息接口，支持开始游戏
func (r *RoomManager) OnExtendLevelInfo(msg *request.PackMessage) int {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("用户不存在 userID:%v", msg.Ext.UserID)
		return ecode.ErrNotFoundUser
	}

	// 检查用户是否已有进行中的关卡游戏
	existingRoomID := r.GetUserLevelRoomID(msg.Ext.AppChannel, msg.Ext.AppID, user.UserID)
	if existingRoomID != 0 {
		room := r.GetRoom(existingRoomID)
		if room != nil && room.IsLevelMode && !room.IsGameEnded {
			// 找到进行中的关卡游戏，直接返回（断线重连场景）
			logx.Infof("用户断线重连到现有关卡游戏 UserID:%s LevelID:%d RoomID:%d",
				user.UserID, room.LevelID, existingRoomID)
			r.sendLevelGameInfo(user, room, msg.MsgID, true) // 明确标记为断线重连
			return ecode.OK
		} else {
			// 清理无效房间
			r.RemoveRoom(existingRoomID)
			r.clearLevelGameState(msg.Ext.AppChannel, msg.Ext.AppID, user.UserID)
		}
	}

	// 没有进行中的关卡游戏，需要解析levelId参数创建新游戏
	var req struct {
		LevelID int `json:"levelId"`
	}
	if err := mapstructure.Decode(msg.Data, &req); err != nil {
		logx.Errorf("解析关卡请求失败 UserID:%s Error:%v", user.UserID, err)
		return ecode.ErrParams
	}

	// 验证levelId参数
	if req.LevelID <= 0 {
		logx.Errorf("无效的关卡ID UserID:%s LevelID:%d", user.UserID, req.LevelID)
		return ecode.ErrParams
	}

	// 验证关卡访问权限
	if !r.validateLevelAccess(msg.Ext.AppChannel, msg.Ext.AppID, user.UserID, req.LevelID) {
		logx.Warnf("关卡权限验证失败 UserID:%s LevelID:%d", user.UserID, req.LevelID)
		return ecode.ErrParams
	}

	// 获取关卡配置
	levelConfig, err := r.getLevelConfig(req.LevelID)
	if err != nil {
		logx.Errorf("获取关卡配置失败 LevelID:%d Error:%v", req.LevelID, err)
		return ecode.ErrNotFoundUser
	}

	// ExtendLevelInfo接口现在只支持开始游戏，不再支持纯查询

	// 创建关卡房间（真实房间）
	room := r.CreateLevelRoom(msg.Ext.AppChannel, msg.Ext.AppID, user.UserID, levelConfig)
	if room == nil {
		logx.Errorf("创建关卡房间失败 UserID:%s LevelID:%d", user.UserID, req.LevelID)
		return ecode.ErrRoomConfig
	}

	// 用户加入房间
	roomUser := &RoomUser{
		UserID:      user.UserID,
		NickName:    user.Nickname, // 修正字段名
		Avatar:      user.Avatar,
		Coin:        user.Coin,
		UserStatus:  constvar.UserStatusSit,
		LastMsgTime: time.Now().Unix(),
		Pos:         0, // 关卡模式固定位置0
	}
	room.UserJoin(roomUser)

	// 设置用户的本地房间信息，让游戏操作消息能正确路由到关卡房间
	localmgr.GetInstance().SetLocal(msg.Ext.AppChannel, msg.Ext.AppID, user.UserID, &localmgr.Local{
		RoomID:    room.RoomID,
		FreshTime: room.CreateTime.Unix(),
	})

	// 启动房间
	room.Start()

	// 保存关卡游戏状态到Redis
	room.saveLevelGameState()

	// 返回关卡游戏信息
	r.sendLevelGameInfo(user, room, msg.MsgID, false) // 明确标记为新游戏

	logx.Infof("关卡游戏创建成功 UserID:%s LevelID:%d RoomID:%d",
		user.UserID, req.LevelID, room.RoomID)

	return ecode.OK
}

// CreateLevelRoom 创建关卡专用房间
func (r *RoomManager) CreateLevelRoom(appChannel string, appID int64, userID string, config *model.LevelConfig) *Room {
	room := NewLevelRoom(appChannel, appID, userID, config)
	if room != nil {
		r.roomMap.Store(room.RoomID, room)
	}
	return room
}

// sendLevelGameInfo 发送关卡游戏信息
func (r *RoomManager) sendLevelGameInfo(user *usermgr.User, room *Room, msgID string, isReconnection bool) {
	// 使用传入的断线重连标识，而不是通过 hasGameProgress() 判断

	// 构建关卡信息响应（统一格式）
	response := map[string]interface{}{
		"levelId":      room.LevelID,
		"mapType":      int(room.MapType),
		"mineCount":    room.mineMap.MineCount,
		"isSpecial":    false, // 从关卡配置获取
		"isUnlocked":   true,
		"isCleared":    false,
		"roomId":       room.RoomID,
		"gameStatus":   int(room.gameStatus),
		"countDown":    room.countDown,
		"reconnected":  isReconnection, // 始终包含重连标识
		"isOnlineMode": false,          // 始终包含模式标识（关卡模式）
	}

	// 如果是断线重连，添加详细的地图状态数据
	if isReconnection {
		response["remainingMines"] = room.mineMap.MineCount - room.getMarkedMineCount()

		// 添加已挖掘和已标记的方块信息
		response["mineMap"] = map[string]interface{}{
			"width":          room.mineMap.Width,
			"height":         room.mineMap.Height,
			"mineCount":      room.mineMap.MineCount,
			"revealedBlocks": room.getRevealedBlocksForClient(),
			"markedBlocks":   room.getMarkedBlocksForClient(),
		}
	}

	// 根据地图类型添加相应字段
	if room.MapType == constvar.MapTypeGrid {
		response["mapWidth"] = room.mineMap.Width
		response["mapHeight"] = room.mineMap.Height
	} else {
		response["validHexes"] = room.mineMap.ValidHexes
		response["neighborMap"] = room.mineMap.NeighborMap
	}

	user.SendMessage(msgID, ecode.OK, response)
}

// validateLevelAccess 验证关卡访问权限
func (r *RoomManager) validateLevelAccess(appChannel string, appID int64, userID string, levelID int) bool {
	// 检查关卡ID是否有效
	if levelID < 1 || levelID > 30 {
		logx.Warnf("关卡ID无效 UserID:%s LevelID:%d", userID, levelID)
		return false
	}

	// 获取用户关卡进度
	progressSearch := model.NewUserLevelProgressSearch()
	_, _, currentLevel, err := progressSearch.GetUserProgress(appChannel, appID, userID)
	if err != nil {
		// 新用户，只能访问第1关
		logx.Infof("新用户权限验证 UserID:%s LevelID:%d 允许访问第1关", userID, levelID)
		return levelID == 1
	}

	// 用户只能访问当前可挑战的关卡
	canAccess := levelID <= currentLevel
	logx.Infof("关卡权限验证 UserID:%s LevelID:%d CurrentLevel:%d CanAccess:%v",
		userID, levelID, currentLevel, canAccess)
	return canAccess
}

// getLevelConfig 获取关卡配置
func (r *RoomManager) getLevelConfig(levelID int) (*model.LevelConfig, error) {
	// 先尝试从数据库获取关卡配置
	search := model.NewLevelConfigSearch().SetLevelID(levelID).SetIsActive(true)
	config, err := search.First()
	if err == nil {
		return config, nil
	}
	//返回错误，未从数据库中找到关卡配置
	return nil, fmt.Errorf("关卡不存在: %v", err)
}

// GetUserLevelRoomID 获取用户的关卡房间ID
func (r *RoomManager) GetUserLevelRoomID(appChannel string, appID int64, userID string) int64 {
	key := fmt.Sprintf("%v:levelGame:%s:%d:%s", conf.Conf.Server.Project, appChannel, appID, userID)
	ctx := context.Background()

	data, err := redisx.GetClient().Get(ctx, key).Result()
	if err != nil {
		return 0
	}

	var state map[string]interface{}
	if err := json.Unmarshal([]byte(data), &state); err != nil {
		return 0
	}

	if roomID, ok := state["roomId"].(float64); ok {
		return int64(roomID)
	}

	return 0
}

// clearLevelGameState 清理关卡游戏状态
func (r *RoomManager) clearLevelGameState(appChannel string, appID int64, userID string) {
	key := fmt.Sprintf("%v:levelGame:%s:%d:%s", conf.Conf.Server.Project, appChannel, appID, userID)
	ctx := context.Background()
	redisx.GetClient().Del(ctx, key)
	logx.Infof("清除关卡游戏状态 UserID:%s", userID)
}
