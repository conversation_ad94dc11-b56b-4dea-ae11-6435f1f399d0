package roommgr

import (
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/constvar"
	"minesweep/ecode"

	"ms-version.soofun.online/wjl/game_public/types_public"
)

// SetSettlement 游戏大结算（扫雷游戏专用）
func (slf *Room) SetSettlement() {
	defer func() {
		if slf.voiceRoom != nil {
			slf.voiceRoom.NoticeGameEnd()
		}
	}()

	// 防止重复结算
	if slf.isSettled {
		logx.Infof("RoomID:%v 已经结算过，跳过重复结算", slf.RoomID)
		return
	}

	slf.isSettled = true

	logx.Infof("开始扫雷游戏大结算 RoomID:%v", slf.RoomID)

	// 计算最终排名（仿照snakes模式，每次都重新计算）
	finalRanking := slf.calculateFinalRanking()

	if len(finalRanking) == 0 {
		logx.Errorf("扫雷游戏结算失败：排名列表为空 RoomID:%v", slf.RoomID)
		return
	}

	// 仿照snakes模式：只执行结算，不发送额外的游戏结束消息
	logx.Infof("开始调用performMinesweeperSettlement RoomID:%v PlayerCount:%d", slf.RoomID, len(finalRanking))
	slf.performMinesweeperSettlement(finalRanking)
	logx.Infof("performMinesweeperSettlement调用完成 RoomID:%v", slf.RoomID)

	logx.Infof("扫雷游戏大结算完成 RoomID:%v PlayerCount:%d", slf.RoomID, len(finalRanking))
}

// ForceCloseRoom 强制关闭房间
func (slf *Room) ForceCloseRoom() {
	if slf.IsRefuseService() {
		logx.Infof("RoomID:%v ForceCloseRoom have refuseService", slf.RoomID)
		return
	}
	logx.Infof("RoomID:%v ForceCloseRoom begin, gameStatus:%v IsLevelMode:%v", slf.RoomID, slf.gameStatus, slf.IsLevelMode)
	slf.SetRefuseService() // 标记房间拒绝服务

	// 关卡房间特殊处理：直接清理，不进行联机模式的结算
	if slf.IsLevelMode {
		slf.forceCloseLevelRoom()
		return
	}

	// 打印房间
	slf.PrintRoom()

	// 游戏中，归还房间费
	if slf.gameStatus > 0 {
		// 定先手游戏状态，游戏强制提前结算(需要初始化slf.allPlayerInfo)
		if slf.gameStatus == constvar.GameStatusFirstMove {
			var sitUsers []*RoomUser
			for _, v := range slf.allUser {
				if v.Pos < 0 {
					continue
				}
				sitUsers = append(sitUsers, v)
			}
			if len(sitUsers) == slf.playerNum {
				for i := 0; i < slf.playerNum; i++ {
					slf.allPlayerInfo[i].SetUser(sitUsers[i])
				}
			}
		}
		for pos := 0; pos < slf.playerNum; pos++ {
			if len(slf.allPlayerInfo[pos].UserID) <= 0 || slf.fee <= 0 {
				continue
			}

			var coinChg = int64(slf.fee) // 房间费
			_, errCode := slf.ChangeBalance(slf.allPlayerInfo[pos].UserID, int(types_public.ActionEventTwo), coinChg, "settle", "settle", constvar.CoinChangeTypeForceCloseRoom, 0, 0)
			if errCode != ecode.OK {
				logx.Errorf("RoomID:%v userID:%v ForceCloseRoom ChangeBalance failed, coinChg:%v, errCode:%v", slf.RoomID, slf.allPlayerInfo[pos].UserID, coinChg, errCode)
			}
		}

		// 上报游戏结束信息
		if slf.voiceRoom != nil || slf.channelCfg.GameReport {
			slf.voiceRoom.NoticeGameEnd()
			// todo 没有上报游戏结束
		}
	}

	// 清理状态机（仅联机模式）
	if !slf.IsLevelMode && slf.phaseManager != nil {
		if err := slf.phaseManager.Cleanup(); err != nil {
			logx.Errorf("强制关闭时状态机清理失败 RoomID:%v Error:%v", slf.RoomID, err)
		}
		slf.phaseManager = nil
	}

	// 移除所有玩家
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		slf.RemoveUser(v.UserID)
	}
	slf.gameStatus = 0 // 因为RemoveRoom是异步的，所以需要把状态置为0，这样定时器就状态检查就不执行了

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("RoomID:%v ForceCloseRoom success", slf.RoomID)
	})
}

// forceCloseLevelRoom 强制关闭关卡房间（不触发联机结算）
func (slf *Room) forceCloseLevelRoom() {
	logx.Infof("强制关闭关卡房间 RoomID:%v LevelID:%d UserID:%s", slf.RoomID, slf.LevelID, slf.LevelUserID)

	// 清理Redis状态
	if slf.LevelUserID != "" {
		GetInstance().clearLevelGameState(slf.appChannel, slf.appID, slf.LevelUserID)
	}

	// 移除所有玩家
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		slf.RemoveUser(v.UserID)
	}
	slf.gameStatus = 0 // 停止定时器

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("关卡房间强制关闭成功 RoomID:%v LevelID:%d", slf.RoomID, slf.LevelID)
	})
}
