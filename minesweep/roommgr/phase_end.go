package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
)

// RoundEndPhaseHandler 已删除，回合结束逻辑已移动到 DisplayPhaseHandler 中

// GameEndPhaseHandler 游戏结束阶段处理器
type GameEndPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *GameEndPhaseHandler) GetPhaseName() string {
	return "GameEnd"
}

// Enter 进入游戏结束阶段
func (h *GameEndPhaseHandler) Enter(room *Room) error {
	// 设置游戏结束状态和展示时间
	room.SetGameStatus(constvar.GameStatusGameEnd, GameEndDisplayTime)

	logx.Infof("进入游戏结束阶段 RoomID:%v Round:%d CountDown:%d",
		room.RoomID, room.currentRound, room.countDown)

	// 执行游戏结束展示逻辑
	return h.executeGameEndDisplay(room)
}

// Tick 游戏结束阶段的每秒处理
func (h *GameEndPhaseHandler) Tick(room *Room) error {
	// 游戏结束阶段立即执行结算，不需要倒计时处理
	// 因为在 Enter 方法中已经调用了 SwitchToSettlement()
	logx.Debugf("游戏结束阶段Tick，已执行结算 RoomID:%v", room.RoomID)
	return nil
}

// Exit 退出游戏结束阶段
func (h *GameEndPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出游戏结束阶段 RoomID:%v", room.RoomID)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *GameEndPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	// 游戏结束是最终状态，通常不转换到其他状态
	// 除非需要重新开始游戏
	return false
}

// executeGameEndDisplay 执行游戏结束展示逻辑
func (h *GameEndPhaseHandler) executeGameEndDisplay(room *Room) error {
	logx.Infof("执行游戏结束展示逻辑 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 1. 广播游戏结束消息（如果需要的话）
	// room.broadcastGameEnd() // 可以根据需要添加

	// 2. 立即执行结算，不需要等待倒计时
	logx.Infof("游戏结束，立即执行结算 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	room.SwitchToSettlement()

	return nil
}
