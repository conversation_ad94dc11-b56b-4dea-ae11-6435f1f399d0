package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
)

// WaitingPhaseHandler 等待阶段处理器
type WaitingPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *WaitingPhaseHandler) GetPhaseName() string {
	return "Waiting"
}

// Enter 进入等待阶段
func (h *WaitingPhaseHandler) Enter(room *Room) error {
	// 设置等待阶段的游戏状态和倒计时
	// 第一次进入时应该是25秒，后续回合也是25秒
	room.SetGameStatus(constvar.GameStatusMinesweeping, MinesweeperRoundTime)

	logx.Infof("进入等待阶段 RoomID:%v Round:%d CountDown:%d",
		room.RoomID, room.currentRound, room.countDown)

	return nil
}

// Tick 等待阶段的每秒处理
func (h *WaitingPhaseHandler) Tick(room *Room) error {
	// 倒计时减1
	room.countDown--
	if room.countDown%5 == 0 || room.countDown < 5 {
		logx.Debugf("等待阶段倒计时 RoomID:%v Round:%d CountDown:%d OperatedPlayers:%d",
			room.RoomID, room.currentRound, room.countDown, len(room.roundActions))
	}

	// AI托管检查和触发移到展示阶段，防止玩家抢夺控制权
	// 这里不再触发AI托管，改为在展示阶段触发

	// AI托管操作执行（开局后3-5秒期间，对应倒计时22-20秒）
	if room.aiManager != nil && room.countDown >= 20 && room.countDown <= 22 {
		room.aiManager.PerformScheduledAIOperations()
	}

	// 检查是否所有玩家都已操作
	if h.allPlayersOperated(room) {
		logx.Infof("所有玩家操作完毕，转换到展示阶段 RoomID:%v Round:%d",
			room.RoomID, room.currentRound)
		return room.phaseManager.TransitionTo(PhaseDisplay)
	}

	// 检查是否到达展示阶段时间点（剩余时间等于展示时间）
	if room.countDown == MinesweeperShowTime {
		logx.Infof("等待阶段倒计时到达展示时间点，转换到展示阶段 RoomID:%v Round:%d CountDown:%d",
			room.RoomID, room.currentRound, room.countDown)
		return room.phaseManager.TransitionTo(PhaseDisplay)
	}

	// 检查是否倒计时结束（异常情况，正常应该在展示时间点转换）
	if room.countDown <= 0 {
		logx.Warnf("等待阶段倒计时异常结束，直接转换到展示阶段 RoomID:%v Round:%d CountDown:%d",
			room.RoomID, room.currentRound, room.countDown)
		return room.phaseManager.TransitionTo(PhaseDisplay)
	}

	return nil
}

// Exit 退出等待阶段
func (h *WaitingPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出等待阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *WaitingPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	switch newPhase {
	case PhaseDisplay:
		return true // 等待阶段可以转换到展示阶段
	case PhaseGameEnd:
		return true // 任何阶段都可以直接转换到游戏结束（异常情况）
	default:
		return false
	}
}

// allPlayersOperated 检查是否所有活跃玩家都已操作
func (h *WaitingPhaseHandler) allPlayersOperated(room *Room) bool {
	activePlayers := room.getActivePlayerCount()
	operatedPlayers := len(room.roundActions)

	/*	logx.Debugf("玩家操作检查 RoomID:%v Round:%d ActivePlayers:%d OperatedPlayers:%d",
		room.RoomID, room.currentRound, activePlayers, operatedPlayers)*/

	return operatedPlayers >= activePlayers
}
