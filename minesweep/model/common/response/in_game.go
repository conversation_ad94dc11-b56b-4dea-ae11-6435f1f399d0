package response

import (
	"minesweep/constvar"
	"minesweep/model/common/base"
)

// 进入房间
type (
	// UserBaseInfo 基础用户信息
	UserBaseInfo struct {
		UserID   string              `json:"userId"`   // 用户ID
		NickName string              `json:"nickName"` // 昵称
		Avatar   string              `json:"avatar"`   // 头像
		Pos      int                 `json:"pos"`      // 座位号
		Coin     int64               `json:"coin"`     // 玩家最新金币
		Status   constvar.UserStatus `json:"status"`   // 玩家状态
	}

	// GameStateInfo 游戏状态信息
	GameStateInfo struct {
		GameScore   int  `json:"gameScore"`   // 当前游戏得分
		RoundScore  int  `json:"roundScore"`  // 当前回合得分
		IsAlive     bool `json:"isAlive"`     // 是否还在游戏中（主动退出的话为false）
		IsAIManaged bool `json:"isAIManaged"` // 是否处于AI托管状态（仅联机模式）
	}

	// OperationInfo 操作相关信息（等待阶段仅本人可用，展示阶段所有人可用）
	OperationInfo struct {
		OperationX    int  `json:"operationX"`    // 操作坐标X
		OperationY    int  `json:"operationY"`    // 操作坐标Y
		OperationType int  `json:"operationType"` // 操作类型
		IsFirstChoice bool `json:"isFirstChoice"` // 是否首选玩家
	}

	// DisplayInfo 展示阶段信息（联机模式扩展字段）
	DisplayInfo struct {
		CurrentRound     int                `json:"currentRound"`     // 当前回合数
		FloodFillResults []*FloodFillResult `json:"floodFillResults"` // 连锁展开结果
	}

	// RoomUser 房间用户信息（使用组合模式）
	RoomUser struct {
		// 嵌入基础用户信息
		UserBaseInfo

		// 嵌入游戏状态信息
		GameStateInfo

		// 嵌入操作信息
		OperationInfo

		// 嵌入展示信息
		DisplayInfo
	}

	// FloodFillResult 连锁展开结果（展示阶段使用）
	FloodFillResult struct {
		TriggerX      int `json:"triggerX"` // 触发坐标X
		TriggerY      int `json:"triggerY"` // 触发坐标Y
		RevealedCells []struct {
			X             int `json:"x"`             // 展开坐标X
			Y             int `json:"y"`             // 展开坐标Y
			NeighborMines int `json:"neighborMines"` // 周围地雷数
		} `json:"revealedCells"` // 展开的方块列表
	}

	EnterRoom struct {
		RoomID       int64               `json:"roomId"`            // 房间ID
		RoomType     constvar.RoomType   `json:"roomType"`          // 房间类型 1-普通场 2-私人场
		PlayerNum    int                 `json:"playerNum"`         // 玩家人数
		MapType      constvar.MapType    `json:"mapType"`           // 地图类型
		Fee          int                 `json:"fee"`               // 房间费
		Users        []*RoomUser         `json:"users"`             // 房间内所有的玩家
		GameStatus   constvar.GameStatus `json:"gameStatus"`        // 房间游戏状态
		CountDown    int                 `json:"countDown"`         // 房间游戏状态倒计时
		MapData      interface{}         `json:"mapData,omitempty"` // 地图数据（包含已挖掘信息）
		IsOnlineMode bool                `json:"isOnlineMode"`      // 是否为联机模式（true-联机模式，false-关卡模式）

		// 联机模式扩展字段
		FirstPlayer string `json:"firstPlayer,omitempty"` // 首选玩家ID
	}
)

// 旁观者列表
type (
	ViewerUser struct {
		UserID   string `json:"userId"`   // 用户ID
		NickName string `json:"nickName"` // 昵称
		Avatar   string `json:"avatar"`   // 头像
	}

	ViewerList struct {
		Viewers []*ViewerUser `json:"viewers"` // 旁观者列表
	}
)

// NoticeUserSitDown 通知玩家坐下
type NoticeUserSitDown struct {
	UserID   string `json:"userId"`   // 玩家Id
	NickName string `json:"nickName"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
	Pos      int    `json:"pos"`      // 座位号
	Coin     int64  `json:"coin"`     // 金币数量
}

// NoticeByUserID 通知用户ID
type NoticeByUserID struct {
	UserID string `json:"userId"`
}

// NoticeStartGame 通知开始游戏
type (
	// HexCoord 六边形坐标结构
	HexCoord struct {
		Q int `json:"q"` // q坐标
		R int `json:"r"` // r坐标
	}

	// MapConfig 地图配置信息
	MapConfig struct {
		Width     int `json:"width"`     // 地图宽度
		Height    int `json:"height"`    // 地图高度
		MineCount int `json:"mineCount"` // 地雷总数
	}

	NoticeStartGame struct {
		RoomID         int64               `json:"roomId"`                   // 房间ID
		RoomType       constvar.RoomType   `json:"roomType"`                 // 房间类型 1-普通场 2-私人场
		PlayerNum      int                 `json:"playerNum"`                // 玩家人数
		MapType        constvar.MapType    `json:"mapType"`                  // 地图类型
		Fee            int                 `json:"fee"`                      // 房间费
		Users          []*RoomUser         `json:"users"`                    // 匹配到的所有玩家
		GameStatus     constvar.GameStatus `json:"gameStatus"`               // 游戏状态
		CountDown      int                 `json:"countDown"`                // 游戏状态倒计时
		ValidHexCoords []*HexCoord         `json:"validHexCoords,omitempty"` // 有效的六边形坐标（六边形地图时使用）
		MapConfig      *MapConfig          `json:"mapConfig,omitempty"`      // 地图配置信息（方形地图时使用）
	}
)

// NoticeFirstMove 通知先手结果
type NoticeFirstMove struct {
	FirstMoves []*base.FirstMove `json:"firstMoves"`
}

// NoticeUserPosList 通知玩家座位号
type (
	UserPos struct {
		UserID string `json:"userId"`
		Pos    int    `json:"pos"`
	}

	NoticeUserPosList struct {
		UserPosList []*UserPos `json:"userPosList"`
	}
)

// NoticeRollDice 通知掷骰子
type NoticeRollDice struct {
	UserID      string              `json:"userId"`      // 玩家Id
	RollTimes   int                 `json:"rollTimes"`   // 本轮已掷骰子次数
	ChessPos    int                 `json:"chessPos"`    // 棋子的位置
	EffectProps []constvar.GameProp `json:"effectProps"` // 有效的的道具列表(反转、倍数、盾牌、冰冻、前进)
}

// NoticeMoveChess 通知移动棋子
type NoticeMoveChess struct {
	UserID           string              `json:"userId"`
	DicePoint        int                 `json:"dicePoint"`        // 单个骰子点数
	OldChessPos      int                 `json:"oldChessPos"`      // 原来棋子的位置
	CurChessPos      int                 `json:"curChessPos"`      // 当前棋子的位置
	Direction        constvar.Direction  `json:"direction"`        // 棋子方向
	EffectProps      []constvar.GameProp `json:"effectProps"`      // 本次掷骰子有效的道具列表(反转、倍数、盾牌、冰冻、前进)
	GameRollTimes    int                 `json:"gameRollTimes"`    // 游戏总掷骰子次数
	CurBlockUserList []string            `json:"curBlockUserList"` // 当前块的用户列表(先到的在前边)
	OldBlockUserList []string            `json:"oldBlockUserList"` // 原来块的用户列表(先到的在前边)
}

// NoticeChoiceProp 通知选择道具
type NoticeChoiceProp struct {
	UserID string              `json:"userId"`
	Props  []constvar.GameProp `json:"props"` // 强化块上的剩余道具列表
}

// NoticeChoicePropResult 通知选择道具的结果
type NoticeChoicePropResult struct {
	UserID    string              `json:"userId"`
	Prop      constvar.GameProp   `json:"prop"`      // 选择的道具
	BlockID   int                 `json:"blockID"`   // 当前强化块ID
	LeftProps []constvar.GameProp `json:"leftProps"` // 当前强化块上的剩余道具列表
}

// NoticeUseProp 通知使用道具
type (
	UserChessPos struct {
		UserID      string `json:"userId"`
		CurChessPos int    `json:"curChessPos"` // 当前棋子的位置
	}

	NoticeUseProp struct {
		UserID        string            `json:"userId"`
		PropType      constvar.GameProp `json:"propType"` // 切换、反转、倍数、暴风雪、前进、红色按钮、盾牌
		ChessPosList  []*UserChessPos   `json:"chessPosList"`
		BlockUserList map[int][]string  `json:"blockUserList"` // 块上的用户列表(先到的在前边)
	}
)

// NoticeSettlement 通知大结算
type (
	UserSettlement struct {
		UserID      string `json:"userId"`      // 玩家Id
		CoinChg     int64  `json:"coinChg"`     // 金币变化
		Coin        int64  `json:"coin"`        // 玩家最新金币
		Rank        int    `json:"rank"`        // 排名
		CurChessPos int    `json:"curChessPos"` // 当前棋子的位置
	}

	NoticeSettlement struct {
		ApiScene constvar.ApiScene `json:"apiScene"` // api调用场景
		Users    []*UserSettlement `json:"users"`    // 闲家结算列表
	}
)

// NoticeUserKickOut 通知玩家被踢出房间
type NoticeUserKickOut struct {
	UserID string `json:"userId"` // 玩家Id
}
