package response

import (
	"fmt"
	"minesweep/constvar"
)

// UserBaseInfo 用户基础信息
type UserBaseInfo struct {
	UserID   string              `json:"userId"`   // 用户ID
	NickName string              `json:"nickName"` // 昵称
	Avatar   string              `json:"avatar"`   // 头像
	Pos      int                 `json:"pos"`      // 座位号
	Coin     int64               `json:"coin"`     // 玩家最新金币
	Status   constvar.UserStatus `json:"status"`   // 玩家状态
}

// GameStateInfo 游戏状态信息（联机模式专用）
type GameStateInfo struct {
	GameScore   int  `json:"gameScore"`   // 当前游戏得分
	IsAlive     bool `json:"isAlive"`     // 是否还在游戏中（主动退出的话为false）
	IsAIManaged bool `json:"isAIManaged"` // 是否处于AI托管状态
}

// OperationInfo 操作信息（等待阶段仅本人可用，展示阶段所有人可用）
type OperationInfo struct {
	OperationX    int  `json:"operationX"`    // 操作坐标X
	OperationY    int  `json:"operationY"`    // 操作坐标Y
	OperationType int  `json:"operationType"` // 操作类型
	IsFirstChoice bool `json:"isFirstChoice"` // 是否首选玩家
}

// DisplayInfo 展示阶段专用信息
type DisplayInfo struct {
	CurrentRound     int                `json:"currentRound"`     // 当前回合数
	FloodFillResults []*FloodFillResult `json:"floodFillResults"` // 连锁展开结果
	PlayerScores     map[string]int     `json:"playerScores"`     // 玩家回合得分
}

// RoomUserV2 重构后的房间用户信息（使用组合模式）
type RoomUserV2 struct {
	BaseInfo    UserBaseInfo   `json:"baseInfo"`              // 基础信息（必需）
	GameState   *GameStateInfo `json:"gameState,omitempty"`   // 游戏状态（联机模式）
	Operation   *OperationInfo `json:"operation,omitempty"`   // 操作信息（有操作时）
	DisplayData *DisplayInfo   `json:"displayData,omitempty"` // 展示数据（展示阶段）
}

// UserInfoBuilder 用户信息构建器
type UserInfoBuilder struct {
	baseInfo    UserBaseInfo
	gameState   *GameStateInfo
	operation   *OperationInfo
	displayData *DisplayInfo
}

// NewUserInfoBuilder 创建用户信息构建器
func NewUserInfoBuilder(userID, nickName, avatar string, pos int, coin int64, status constvar.UserStatus) *UserInfoBuilder {
	return &UserInfoBuilder{
		baseInfo: UserBaseInfo{
			UserID:   userID,
			NickName: nickName,
			Avatar:   avatar,
			Pos:      pos,
			Coin:     coin,
			Status:   status,
		},
	}
}

// WithGameState 添加游戏状态信息
func (b *UserInfoBuilder) WithGameState(gameScore int, isAlive, isAIManaged bool) *UserInfoBuilder {
	b.gameState = &GameStateInfo{
		GameScore:   gameScore,
		IsAlive:     isAlive,
		IsAIManaged: isAIManaged,
	}
	return b
}

// WithOperation 添加操作信息
func (b *UserInfoBuilder) WithOperation(x, y, opType int, isFirstChoice bool) *UserInfoBuilder {
	b.operation = &OperationInfo{
		OperationX:    x,
		OperationY:    y,
		OperationType: opType,
		IsFirstChoice: isFirstChoice,
	}
	return b
}

// WithDisplayData 添加展示数据
func (b *UserInfoBuilder) WithDisplayData(currentRound int, floodFillResults []*FloodFillResult, playerScores map[string]int) *UserInfoBuilder {
	b.displayData = &DisplayInfo{
		CurrentRound:     currentRound,
		FloodFillResults: floodFillResults,
		PlayerScores:     playerScores,
	}
	return b
}

// Build 构建最终的用户信息
func (b *UserInfoBuilder) Build() *RoomUserV2 {
	return &RoomUserV2{
		BaseInfo:    b.baseInfo,
		GameState:   b.gameState,
		Operation:   b.operation,
		DisplayData: b.displayData,
	}
}

// BuildLegacy 构建兼容旧版本的用户信息
func (b *UserInfoBuilder) BuildLegacy() *RoomUser {
	user := &RoomUser{
		UserID:   b.baseInfo.UserID,
		NickName: b.baseInfo.NickName,
		Avatar:   b.baseInfo.Avatar,
		Pos:      b.baseInfo.Pos,
		Coin:     b.baseInfo.Coin,
		Status:   b.baseInfo.Status,
	}
	
	// 填充游戏状态
	if b.gameState != nil {
		user.GameScore = b.gameState.GameScore
		user.IsAlive = b.gameState.IsAlive
		user.IsAIManaged = b.gameState.IsAIManaged
	}
	
	// 填充操作信息
	if b.operation != nil {
		user.OperationX = b.operation.OperationX
		user.OperationY = b.operation.OperationY
		user.OperationType = b.operation.OperationType
		user.IsFirstChoice = b.operation.IsFirstChoice
	}
	
	// 填充展示数据
	if b.displayData != nil {
		user.CurrentRound = b.displayData.CurrentRound
		user.FloodFillResults = b.displayData.FloodFillResults
		user.PlayerScores = b.displayData.PlayerScores
	}
	
	return user
}

// UserInfoFactory 用户信息工厂
type UserInfoFactory struct {
	room interface{} // Room 接口，避免循环依赖
}

// CreateOnlineUser 创建联机模式用户信息
func (f *UserInfoFactory) CreateOnlineUser(roomUser interface{}, requestUserID string, gameStatus constvar.GameStatus) *RoomUser {
	// 这里需要根据实际的 RoomUser 结构体来实现
	// 为了避免循环依赖，使用 interface{} 类型
	return nil
}

// CreateLevelUser 创建关卡模式用户信息
func (f *UserInfoFactory) CreateLevelUser(roomUser interface{}) *RoomUser {
	// 关卡模式用户信息构建逻辑
	return nil
}

// UserInfoValidator 用户信息验证器
type UserInfoValidator struct{}

// ValidateBaseInfo 验证基础信息
func (v *UserInfoValidator) ValidateBaseInfo(info UserBaseInfo) error {
	if info.UserID == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	if info.NickName == "" {
		return fmt.Errorf("昵称不能为空")
	}
	if info.Pos < 0 {
		return fmt.Errorf("座位号不能为负数")
	}
	return nil
}

// ValidateGameState 验证游戏状态
func (v *UserInfoValidator) ValidateGameState(state *GameStateInfo) error {
	if state == nil {
		return nil // 游戏状态可以为空
	}
	// 游戏得分可以为负数（踩雷扣分）
	return nil
}

// ValidateOperation 验证操作信息
func (v *UserInfoValidator) ValidateOperation(op *OperationInfo) error {
	if op == nil {
		return nil // 操作信息可以为空
	}
	if op.OperationX < 0 || op.OperationY < 0 {
		return fmt.Errorf("操作坐标不能为负数")
	}
	if op.OperationType < 1 || op.OperationType > 2 {
		return fmt.Errorf("操作类型必须为1(挖掘)或2(标记)")
	}
	return nil
}

// ValidateDisplayInfo 验证展示信息
func (v *UserInfoValidator) ValidateDisplayInfo(display *DisplayInfo) error {
	if display == nil {
		return nil // 展示信息可以为空
	}
	if display.CurrentRound < 0 {
		return fmt.Errorf("当前回合数不能为负数")
	}
	return nil
}

// Validate 验证完整的用户信息
func (v *UserInfoValidator) Validate(user *RoomUserV2) error {
	if err := v.ValidateBaseInfo(user.BaseInfo); err != nil {
		return fmt.Errorf("基础信息验证失败: %w", err)
	}
	if err := v.ValidateGameState(user.GameState); err != nil {
		return fmt.Errorf("游戏状态验证失败: %w", err)
	}
	if err := v.ValidateOperation(user.Operation); err != nil {
		return fmt.Errorf("操作信息验证失败: %w", err)
	}
	if err := v.ValidateDisplayInfo(user.DisplayData); err != nil {
		return fmt.Errorf("展示信息验证失败: %w", err)
	}
	return nil
}
