package request

import "minesweep/constvar"

// PairRequest 请求匹配
type PairRequest struct {
	PlayerNum int `json:"playerNum"`
	Fee       int `json:"fee"`
	//GridNum   int `json:"gridNum"`
	//MapType int `json:"mapType"` // 地图类型（0-方格，1-六边形）
	//PropMode int `json:"propMode"` // 0 无道具模式 1 有道具模式
}

// EnterRoom 请求进入房间
type EnterRoom struct {
	LastRoomID int64 `json:"lastRoomID"` // 上一次房间ID
}

// LeaveRoom 请求离开房间
type LeaveRoom struct {
	IsConfirmLeave bool `json:"isConfirmLeave"` // 确认离开房间
}

// SitDown 请求坐下
type SitDown struct {
	Pos int `json:"pos"` // 座位号
}

// RobotSitDown 机器人请求坐下
type RobotSitDown struct {
	UserID   string `json:"userId"`   // 玩家ID
	NickName string `json:"nickName"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
}

// UseProp 使用游戏道具
type UseProp struct {
	PropType constvar.GameProp `json:"propType"`
}

// ChoiceProp 选择游戏道具
type ChoiceProp struct {
	PropType constvar.GameProp `json:"propType"`
}

// ChoiceAdvance 玩家选择前进点数
type ChoiceAdvance struct {
	DicePoint int `json:"dicePoint"` // 前进点数
}

// RollDice 玩家请求掷骰子
type RollDice struct {
	DicePoint int `json:"dicePoint"` // 点数
}

// MoveChessEnd 玩家请求移动骰子结束
type MoveChessEnd struct {
	GameRollTimes int `json:"gameRollTimes"` // 游戏总掷骰子次数
}
