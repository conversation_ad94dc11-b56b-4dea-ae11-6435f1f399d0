# Minesweep 游戏 WebSocket API 文档

## 概述

本文档描述了 Minesweep 游戏的 WebSocket API 接口。所有消息都通过 WebSocket 连接进行通信。

## 消息格式

### 请求消息格式
```json
{
  "msgId": "消息类型",
  "data": {
    // 具体参数
  }
}
```

### 响应消息格式
```json
{
  "msgId": "消息类型",
  "code": 0,
  "msg": "success",
  "data": {
    // 响应数据
  }
}
```

## 接口目录

### 基础接口
1. [登录接口](#1-登录接口)
2. [请求匹配](#2-请求匹配)

### 游戏房间相关接口
3. [创建房间](#3-创建房间)
4. [加入房间](#4-加入房间)
5. [房间准备](#5-房间准备)
6. [开始游戏](#6-开始游戏)
7. [扫雷操作](#7-扫雷操作)
8. [获取房间信息](#8-获取房间信息)
9. [获取房间列表](#9-获取房间列表)
10. [房间聊天](#10-房间聊天)
11. [房间表情](#11-房间表情)
12. [房间礼物](#12-房间礼物)
13. [踢出玩家](#13-踢出玩家)
14. [修改房间配置](#14-修改房间配置)
15. [离开房间](#15-离开房间)

### 单机关卡系统接口
14. [获取关卡进度](#14-获取关卡进度)
15. [获取关卡详情](#15-获取关卡详情)

### 语聊房相关接口
17. [进入语聊房](#17-进入语聊房)
18. [语聊房坐下](#18-语聊房坐下)
19. [语聊房站起](#19-语聊房站起)
20. [修改语聊房配置](#20-修改语聊房配置)
21. [语聊房准备](#21-语聊房准备)
22. [语聊房开始游戏](#22-语聊房开始游戏)
23. [语聊房踢人](#23-语聊房踢人)

### 通知类消息
24. [匹配结果通知](#24-匹配结果通知)
25. [扫雷回合开始通知](#25-扫雷回合开始通知)
26. [扫雷操作展示通知](#26-扫雷操作展示通知)
27. [扫雷回合结束通知](#27-扫雷回合结束通知)
28. [扫雷首选玩家奖励推送通知](#28-扫雷首选玩家奖励推送通知)
29. [扫雷游戏结束通知](#29-扫雷游戏结束通知)

### 关卡模式游戏接口（单人游戏）
30. [获取关卡信息](#30-获取关卡信息)
31. [获取关卡进度](#31-获取关卡进度)
32. [关卡点击方块](#32-关卡点击方块)
33. [离开房间](#33-离开房间)

### 关卡模式通知接口
34. [关卡游戏结束通知](#34-关卡游戏结束通知)

### AI托管系统接口
35. [AI托管状态变更通知](#35-ai托管状态变更通知)

## API 接口列表

### 1. 登录接口

**WebSocket消息类型**：`"Login"`

玩家登录游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 否 | String | app的语聊房Id，非语聊房赋值为空 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userInfo | - | 是 | Object | 用户信息，参考UserInfo结构 |
| roomId | - | 是 | Number | 游戏中房间ID（>0表示断线重连） |
| inviteCode | - | 是 | Number | 邀请码（>0表示已创建房间） |
| hideCoin | - | 是 | Boolean | 是否隐藏金币 |
| roomConfigs | - | 是 | Array | 房间配置列表 |

#### UserInfo结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | - | 是 | String | 玩家ID |
| nickname | - | 是 | String | 昵称 |
| avatar | - | 是 | String | 头像 |
| coin | - | 是 | Number | 当前金币 |
| serverTime | - | 是 | Number | 服务器的秒级时间戳 |

### 2. 请求匹配

**WebSocket消息类型**：`"PairRequest"`

玩家请求匹配其他玩家开始游戏。地图类型由服务端随机生成。

**匹配流程说明**：
1. 客户端发送匹配请求
2. 服务端立即响应（表示已加入匹配队列）
3. 匹配成功后，服务端发送 `GameStart` 通知（包含完整游戏信息）

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| playerNum | - | 是 | Number | 玩家人数（2-4） |
| fee | - | 是 | Number | 房间费用 |

#### 响应参数

成功时返回空对象 `{}`（表示已成功加入匹配队列）

**注意**：匹配成功后会收到 `GameStart` 通知消息，包含房间ID、地图类型等完整游戏信息。

### 3. 取消匹配

**WebSocket消息类型**：`"CancelPair"`

玩家取消匹配请求。

#### 请求参数

无参数

#### 响应参数

成功时返回空对象 `{}`

### 4. 创建邀请

**WebSocket消息类型**：`"CreateInvite"`

创建邀请房间。地图类型由服务端随机生成。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| playerNum | - | 是 | Number | 玩家人数（2-4） |
| fee | - | 是 | Number | 房间费用 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| inviteCode | - | 是 | Number | 邀请码 |
| inviteInfo | - | 是 | Object | 邀请信息，参考InviteInfo结构 |

### 5. 接受邀请

**WebSocket消息类型**：`"AcceptInvite"`

接受邀请加入房间。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| inviteCode | - | 是 | Number | 邀请码 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | - | 是 | String | 玩家ID |
| inviteInfo | - | 是 | Object | 邀请信息，参考InviteInfo结构 |

### 6. 邀请准备

**WebSocket消息类型**：`"InviteReady"`

邀请房间中玩家改变准备状态。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| ready | - | 是 | Boolean | 是否准备 |

#### 响应参数

成功时返回空对象 `{}`

### 8. 坐下

**WebSocket消息类型**：`"SitDown"`

玩家请求坐下。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| pos | - | 是 | Number | 座位号 |

#### 响应参数

成功时返回空对象 `{}`

### 9. 准备

**WebSocket消息类型**：`"Ready"`

玩家请求准备。

#### 请求参数

无参数

#### 响应参数

成功时返回空对象 `{}`

### 10. 断线重连接口

**WebSocket消息类型**：`"EnterRoom"`

断线重连统一使用 `EnterRoom` 消息类型，服务端根据房间类型自动判断是联机模式还是关卡模式。

#### 请求参数

断线重连不需要任何请求参数，只需发送空的 `EnterRoom` 消息即可。

#### 响应参数

服务端根据房间类型返回不同的响应：

**1. 关卡模式响应（简化版）**

当检测到是关卡模式时，只返回基本信息让前端识别模式：

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | Number | 房间ID |
| roomType | 1/2 | 是 | Number | 房间类型 |
| gameStatus | 0-5 | 是 | Number | 游戏状态 |
| countDown | -1 | 是 | Number | 倒计时（关卡模式固定-1） |
| isOnlineMode | false | 是 | Boolean | 模式标识（关卡模式固定false） |

**2. 联机模式响应（完整版）**

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | Number | 房间ID |
| roomType | 1/2/3 | 是 | Number | 房间类型（1-普通场，2-私人场，3-语聊房场） |
| playerNum | 2-4 | 是 | Number | 玩家人数，支持2-4人房间 |
| mapType | 0/1 | 是 | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | 0/500/1000 | 是 | Number | 房间费用，0表示免费房间 |
| users | - | 是 | Array | 房间内所有玩家列表，参考RoomUser结构 |
| gameStatus | 0-5 | 是 | Number | 游戏状态（0-等待开始，1-扫雷进行中，2-回合结束展示，3-游戏结束，4-关卡胜利，5-关卡失败） |
| countDown | 0-25 | 是 | Number | 当前游戏状态的倒计时秒数 |
| mapData | - | 否 | Object | 地图数据（包含已挖掘和已标记的方块信息），仅在游戏进行中返回 |
| isOnlineMode | true | 是 | Boolean | 模式标识（联机模式固定true） |

#### RevealedBlockInfo结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | X坐标 |
| y | - | 是 | Number | Y坐标 |
| q | - | 否 | Number | 六边形Q坐标（仅六边形地图） |
| r | - | 否 | Number | 六边形R坐标（仅六边形地图） |
| neighborMines | - | 是 | Number | 周围地雷数 |

#### MarkedBlockInfo结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | X坐标 |
| y | - | 是 | Number | Y坐标 |
| q | - | 否 | Number | 六边形Q坐标（仅六边形地图） |
| r | - | 否 | Number | 六边形R坐标（仅六边形地图） |

#### 断线重连流程说明

1. **前端检测**：用户登录后，如果返回的 `roomId > 0`，表示用户有未完成的游戏
2. **发送请求**：前端发送 `EnterRoom` 消息进行断线重连
3. **服务端判断**：
   - 关卡模式：返回简化响应，包含 `isOnlineMode: false`
   - 联机模式：返回完整响应，包含 `isOnlineMode: true`
4. **前端处理**：
   - 如果 `isOnlineMode: false`，前端再发送 `ExtendLevelInfo` 请求获取关卡详情
   - 如果 `isOnlineMode: true`，直接使用返回的完整数据恢复游戏状态
#### 游戏状态值说明

**统一游戏状态值**（所有接口使用相同的状态值体系）：
- `0` = 等待开始
- `1` = 扫雷进行中
- `2` = 回合结束展示（仅联机模式）
- `3` = 游戏结束（联机模式）
- `4` = 关卡胜利（关卡模式）
- `5` = 关卡失败（关卡模式）

#### 断线重连示例

**请求示例**：

```json
{
  "msgId": "EnterRoom",
  "data": {}
}
```

**关卡模式响应示例**：

```json
{
  "msgId": "EnterRoom",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12345,
    "roomType": 1,
    "gameStatus": 1,
    "countDown": -1,
    "isOnlineMode": false
  }
}
```

**联机模式响应示例**：

```json
{
  "msgId": "EnterRoom",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12345,
    "roomType": 1,
    "playerNum": 4,
    "mapType": 0,
    "fee": 500,
    "gameStatus": 1,
    "countDown": 15,
    "isOnlineMode": true,
    "firstPlayer": "user123",
    "users": [
      {
        "userId": "user123",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 0,
        "coin": 10000,
        "status": 2,
        "gameScore": 50,
        "isAlive": true,
        "isAIManaged": false,
        "operationX": 3,
        "operationY": 4,
        "operationType": 1,
        "isFirstChoice": true
      }
    ],
    "mapData": {
      "blocks": [
        [
          {
            "isRevealed": true,
            "isMarked": false,
            "neighborMines": 1
          }
        ]
      ]
    }
  }
}

}
```

### 11. 点击方块

**WebSocket消息类型**：`"ClickBlock"`

玩家在扫雷游戏中点击方块进行操作。

**使用场景**：
- **联机模式**：在扫雷游戏中使用（支持方格地图mapType=0和六边形地图mapType=1）
  - 在回合时间内（25秒）可以操作
  - 每回合每个玩家只能操作一次，后续操作会覆盖前面的操作
- **关卡模式**：单人关卡游戏中使用
  - 无时间限制，可以随时操作
  - 无回合概念，支持连续操作
  - 系统自动识别当前用户的关卡房间

#### 请求参数

**坐标参数根据地图类型而不同**：

##### 方格地图（mapType=0）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | 方块x坐标（0-7，从左到右） |
| y | - | 是 | Number | 方块y坐标（0-7，从下到上，左下角为(0,0)） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

##### 六边形地图（mapType=1）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | 六边形q坐标（轴向坐标系统） |
| y | - | 是 | Number | 六边形r坐标（轴向坐标系统） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

**六边形坐标系统说明**：

- 六边形地图使用轴向坐标系统(q,r)
- x参数传递q坐标值，y参数传递r坐标值
- 坐标可以为负数，例如：x=1, y=-1 表示坐标(1,-1)
- 六边形的6个邻居方向：(+1,0), (+1,-1), (0,-1), (-1,0), (-1,+1), (0,+1)

#### 请求示例

##### 方格地图请求示例
```json
{
  "msgId": "ClickBlock",
  "data": {
    "x": 3,
    "y": 2,
    "action": 1
  }
}
```

##### 六边形地图请求示例
```json
{
  "msgId": "ClickBlock",
  "data": {
    "x": 1,
    "y": -1,
    "action": 1
  }
}
```

#### 响应参数

成功时返回空对象 `{}`

**操作类型说明**：
- `action=1`：挖掘方块，揭示方块内容（数字或地雷）
- `action=2`：标记/取消标记地雷，切换方块的标记状态

**联机模式注意事项**：
- 前20秒操作不会立即广播给其他玩家
- 第20秒后会统一显示所有玩家的操作
- 多个玩家可以选择同一个方块
- 标记操作（action=2）会自动切换标记状态：未标记→标记，标记→取消标记

**关卡模式注意事项**：
- 操作立即生效，无延迟展示
- 单人游戏，无其他玩家干扰
- 支持连续快速操作
- 踩雷或完成游戏后需调用结束游戏接口

### 13. 离开房间

**WebSocket消息类型**：`"LeaveRoom"`

玩家主动离开房间。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| isConfirmLeave | - | 是 | Boolean | 确认离开房间 |

#### 响应参数

成功时返回空对象 `{}`

## 单机关卡系统接口

###

### 15. 获取关卡进度

**WebSocket消息类型**：`"ExtendLevelProgress"`

获取用户当前的关卡进度信息。

#### 请求参数

无参数

#### 响应参数

| 参数名        | 参数值 | 是否必填 | 参数类型 | 描述说明         |
| ------------- | ------ | -------- | -------- | ---------------- |
| totalLevels   | 30     | 是       | Number   | 总关卡数         |
| clearedLevels | 5      | 是       | Number   | 已通关关卡数     |
| currentLevel  | 6      | 是       | Number   | 当前应挑战的关卡 |

#### 响应示例

```json
{
  "msgId": "ExtendLevelProgress",
  "code": 0,
  "msg": "success",
  "data": {
    "totalLevels": 30,
    "clearedLevels": 5,
    "currentLevel": 6
  }
}
```

## 语聊房相关接口

### 17. 进入语聊房

**WebSocket消息类型**：`"EnterVoiceRoom"`

进入语聊房。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| platRoomID | - | 是 | String | 语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |
| users | - | 是 | Array | 玩家列表，参考VoiceRoomUser结构 |
| roomId | - | 是 | Number | 与之相关的游戏房间Id |
| kickOut | - | 是 | Boolean | 是否允许踢除玩家 |

### 19. 语聊房坐下

**WebSocket消息类型**：`"VoiceUserSit"`

在语聊房中坐下。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| pos | - | 是 | Number | 请求坐下的游戏位置 |

#### 响应参数

成功时返回空对象 `{}`

### 20. 语聊房站起

**WebSocket消息类型**：`"VoiceUserStandUp"`

在语聊房中站起。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 21. 修改语聊房配置

**WebSocket消息类型**：`"ChangeVoiceCfg"`

修改语聊房配置。地图类型由服务端随机生成。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |

#### 响应参数

成功时返回空对象 `{}`

### 22. 语聊房准备

**WebSocket消息类型**：`"VoiceUserReady"`

语聊房玩家准备。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| ready | - | 是 | Boolean | 是否准备 |

#### 响应参数

成功时返回空对象 `{}`

### 23. 语聊房开始游戏

**WebSocket消息类型**：`"VoiceStartGame"`

语聊房开始游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 24. 语聊房踢人

**WebSocket消息类型**：`"VoiceKickOut"`

语聊房踢除玩家。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| userId | - | 是 | String | 玩家ID |

#### 响应参数

成功时返回空对象 `{}`

## 通知类消息

### 25. 匹配结果通知

**WebSocket消息类型**：`"GameStart"`

服务端通知客户端匹配到了其他玩家并开始游戏。此通知在 `PairRequest` 匹配成功后自动发送，包含完整的游戏房间信息和随机生成的地图类型。

**地图类型说明**：

- mapType=0：方形地图，响应中包含 `mapConfig` 字段
- mapType=1：六边形地图，响应中包含 `validHexCoords` 字段

**扫雷游戏流程**：

1. 发送 `GameStart` 通知
2. 前端播放2秒开场动画
3. 2秒后发送 `NoticeRoundStart` 通知，正式开始游戏

#### 通知参数

| 参数名         | 参数值     | 是否必填 | 参数类型 | 描述说明                                                    |
|--------|--------|----------|----------|----------|
| roomId         | 12345      | 是       | Number   | 房间ID，唯一标识房间                                        |
| roomType       | 1/2/3      | 是       | Number   | 房间类型（1-普通场，2-私人场，3-语聊房场）                  |
| playerNum      | 2-4        | 是       | Number   | 玩家人数，支持2-4人房间                                     |
| mapType        | 0/1        | 是       | Number   | 地图类型（0-方格地图，1-六边形地图）**由服务端随机生成**    |
| fee            | 0/500/1000 | 是       | Number   | 房间费用，0表示免费房间                                     |
| users          | -          | 是       | Array    | 匹配到的所有玩家，参考RoomUser结构                          |
| gameStatus     | 0-5        | 是       | Number   | 游戏状态（0-等待开始，1-扫雷进行中，2-回合结束展示，3-游戏结束，4-关卡胜利，5-关卡失败） |
| countDown      | 0-25       | 是       | Number   | 当前游戏状态的倒计时秒数                                    |
| validHexCoords | -          | 否       | Array    | 有效的六边形坐标列表（仅mapType=1时返回），参考HexCoord结构 |
| mapConfig      | -          | 否       | Object   | 地图配置信息（仅mapType=0时返回），参考MapConfig结构        |

#### MapConfig结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| width | 8 | 是 | Number | 地图宽度（8） |
| height | 8 | 是 | Number | 地图高度（8） |
| mineCount | 13 | 是 | Number | 地雷总数（13） |

#### HexCoord 结构（六边形地图专用）

| 参数名 | 参数值 | 参数类型 | 是否必填 | 描述说明          |
| ------ | ------ | -------- | -------- | ----------------- |
| q      | 0      | Number   | 是       | 六边形坐标系q坐标 |
| r      | 0      | Number   | 是       | 六边形坐标系r坐标 |



#### 响应示例

**方形地图响应示例（mapType=0）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12345,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 0,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "skinChessId": 1001
      }
    ],
    "gameStatus": 1,
    "countDown": 0,
    "mapConfig": {
      "width": 8,
      "height": 8,
      "mineCount": 13
    }
  }
}
```

**六边形地图响应示例（mapType=1）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12346,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 1,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "minesFound": 0,
        "blocksRevealed": 0,
        "gameScore": 0,
        "isAlive": true
      }
    ],
    "gameStatus": 1,
    "countDown": 300,
    "validHexCoords": [
      { "q": 0, "r": 0 },
      { "q": 1, "r": 0 },
      { "q": 1, "r": -1 },
      { "q": 0, "r": -1 },
      { "q": -1, "r": 0 },
      { "q": -1, "r": 1 },
      { "q": 0, "r": 1 }
    ]
  }
}
```

### 26. 扫雷回合开始通知

**WebSocket消息类型**：`"NoticeRoundStart"`

服务端通知客户端扫雷游戏回合开始。此通知在游戏开始2秒延迟后自动发送，标志着扫雷游戏正式开始。

**发送时机**：
- 在 `GameStart` 发送2秒后自动发送
- 后端同时开始25秒回合倒计时
- 前端收到此消息后立即进入游戏状态

**回合时间机制**：
- **前20秒**：玩家可以进行挖掘和标记操作
- **后5秒**：展示阶段，显示所有玩家操作但不允许新操作
- **第25秒**：回合正式结束，发送NoticeRoundEnd通知

**AI托管机制**：
- **触发时机**：第20秒时，为未选择格子的玩家执行AI托管
- **托管行为**：系统随机选择一格进行操作（70%概率挖掘，30%概率标记）
- **奖励规则**：AI托管状态下不享受首选玩家+1分奖励
- **优先级**：优先选择未被挖掘的格子，如无可用格子则随机选择
- **展示效果**：AI托管后立即在NoticeActionDisplay中显示所有玩家操作

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 回合编号（从1开始） |
| countDown | 25 | 是 | Number | 回合倒计时（25秒） |
| gameStatus | 0 | 是 | Number | 游戏状态（0-扫雷进行中） |

#### 响应示例

```json
{
  "msgId": "NoticeRoundStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "countDown": 25,
    "gameStatus": 0
  }
}
```

### 27. 扫雷操作展示通知

**WebSocket消息类型**：`"NoticeActionDisplay"`

服务端通知客户端进入操作展示阶段。此通知在第20秒时发送，包含完整的游戏数据供前端展示5秒动画效果。

**发送时机**：
- 当倒计时从6变为5时自动发送
- 或所有玩家操作完毕时立即发送
- 先为未操作玩家执行AI托管，再发送此通知
- 包含完整的操作结果、地图数据和得分信息
- 进入5秒展示阶段，不允许新操作

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 当前回合编号 |
| gameStatus | 0 | 是 | Number | 游戏状态（0-扫雷进行中） |
| countDown | 5 | 是 | Number | 剩余倒计时（5秒展示阶段） |
| playerActions | - | 是 | Array | 玩家操作结果列表，参考PlayerAction结构 |
| floodFillResults | - | 否 | Array | 连锁展开结果列表，参考FloodFillData结构 |
| playerTotalScores | - | 是 | Object | 玩家累计总得分对象，属性名为userID，属性值为分数(Number) |
| remainingMines | 8 | 是 | Number | 剩余地雷数量 |
| message | "展示阶段：显示所有玩家操作和得分" | 是 | String | 提示信息 |

#### PlayerAction结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "1" | 是 | String | 玩家ID |
| x | 3 | 是 | Number | 操作坐标x（六边形地图中对应q坐标） |
| y | 2 | 是 | Number | 操作坐标y（六边形地图中对应r坐标） |
| action | 1 | 是 | Number | 操作类型（1-挖掘，2-标记） |
| score | 10 | 是 | Number | 本次操作得分（基础得分，不含首选奖励） |
| isFirstChoice | true | 是 | Boolean | 是否为首选玩家 |
| result | 2/"mine"/"correct_mark"/"wrong_mark" | 是 | Number/String | 操作结果（挖掘：周围地雷数或"mine"；标记："correct_mark"或"wrong_mark"） |

**操作结果说明**：
- **挖掘操作**：返回周围地雷数（0-8或0-6），如果是地雷则返回"mine"
- **标记操作**：返回"correct_mark"（正确标记）或"wrong_mark"（错误标记）

#### FloodFillData结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| triggerUserId | "1" | 是 | String | 触发连锁展开的玩家ID |
| triggerX | 3 | 是 | Number | 触发点X坐标 |
| triggerY | 2 | 是 | Number | 触发点Y坐标 |
| revealedBlocks | - | 是 | Array | 连锁揭示的方块列表，参考RevealedBlock结构 |

#### RevealedBlock结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | 3 | 是 | Number | 方块X坐标 |
| y | 2 | 是 | Number | 方块Y坐标 |
| neighborMines | 1 | 是 | Number | 周围地雷数量 |

#### 响应示例

```json
{
  "msgId": "NoticeActionDisplay",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 0,
    "countDown": 5,
    "playerActions": [
      {
        "userId": "1",
        "x": 3,
        "y": 2,
        "action": 1,
        "score": 15,
        "isFirstChoice": true,
        "result": 2
      },
      {
        "userId": "2",
        "x": 1,
        "y": 4,
        "action": 2,
        "score": 5,
        "isFirstChoice": false,
        "result": "correct_mark"
      }
    ],
    "floodFillResults": [
      {
        "triggerUserId": "1",
        "triggerX": 3,
        "triggerY": 2,
        "revealedBlocks": [
          {"x": 2, "y": 2, "neighborMines": 1},
          {"x": 4, "y": 2, "neighborMines": 0},
          {"x": 3, "y": 1, "neighborMines": 1}
        ]
      }
    ],
    "playerTotalScores": {
      "1": 25,
      "2": 15
    },
    "remainingMines": 8,
    "message": "展示阶段：显示玩家操作结果"
  }
}
```

### 28. 扫雷回合结束通知

**WebSocket消息类型**：`"NoticeRoundEnd"`

服务端通知客户端扫雷游戏回合正式结束。此通知在25秒回合时间结束后自动发送，纯状态通知，确认回合结束。详细的操作数据已在NoticeActionDisplay中发送。

**发送时机**：
- 25秒回合倒计时结束后自动发送
- 在NoticeActionDisplay发送5秒后
- 纯状态通知，确认回合结束
- 准备进入下一回合或游戏结束判断

**接口简化说明**：
- **数据已前置**：详细的操作结果、地图数据等已在NoticeActionDisplay中发送
- **职责分离**：本接口专注于状态通知，不重复发送数据
- **前端优势**：前端在第20秒就获得完整数据，有5秒时间展示动画

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 当前回合编号 |
| gameStatus | 1 | 是 | Number | 游戏状态（1-回合结束） |
| message | "回合结束" | 是 | String | 回合结束消息 |



#### 响应示例

```json
{
  "msgId": "NoticeRoundEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 1,
    "message": "回合结束"
  }
}
```

---

### 29. 扫雷首选玩家奖励推送通知

**WebSocket消息类型**：`"NoticeFirstChoiceBonus"`

服务端通知客户端首选玩家奖励。此通知在玩家成为首选玩家（第一个在当前回合进行挖掘或标记操作）时立即发送，只包含首选玩家的+1分奖励。

**发送时机**：
- 玩家成为首选玩家时立即发送
- 只发送给首选玩家本人
- 只包含+1分的首选玩家奖励，不包含基础得分
- 与基础得分（挖掘/标记得分）分离推送

**奖励规则**：
- **首选玩家奖励**：固定+1分
- **仅真实玩家操作**：AI托管不享受此奖励
- **无论操作对错**：不管挖掘/标记结果如何都给奖励
- **每回合只有一个**：只有第一个操作的玩家享受

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| roundNumber | 1 | 是 | Number | 回合编号 |
| bonusScore | 1 | 是 | Number | 首选玩家奖励分数（固定+1） |
| totalScore | 15 | 是 | Number | 累计总得分（包含此奖励） |

#### 响应示例

```json
{
  "msgId": "NoticeFirstChoiceBonus",
  "code": 0,
  "msg": "success",
  "data": {
    "userId": "player_001",
    "roundNumber": 1,
    "bonusScore": 1,
    "totalScore": 15
  }
}
```

### 30. 扫雷游戏结算通知

**WebSocket消息类型**：`"Settlement"`

服务端通知客户端扫雷游戏结算结果。此通知在游戏结束时发送，包含金币分配和游戏统计信息。

**发送时机**：
- 游戏结束后立即发送
- 在NoticeGameEnd之前发送
- 包含金币变化和奖励分配信息
- 提供游戏统计数据

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| gameType | "minesweeper" | 是 | String | 游戏类型标识 |
| totalRounds | 5 | 是 | Number | 总回合数 |
| playerCount | 2 | 是 | Number | 玩家数量 |
| finalRanking | - | 是 | Array | 最终排名列表，参考PlayerFinalResult结构 |
| fee | 100 | 是 | Number | 房间费用 |
| gameStats | - | 是 | Object | 游戏统计信息 |

#### GameStats结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| mapSize | "8x8" | 是 | String | 地图大小 |
| mineCount | 13 | 是 | Number | 地雷总数 |
| revealedCount | 45 | 是 | Number | 已揭示的方块数 |

#### PlayerFinalResult结构 ####

| 参数名     | 参数值       | 是否必填 | 参数类型 | 描述说明                             |
| ---------- | ------------ | -------- | -------- | ------------------------------------ |
| userId     | "player_001" | 是       | String   | 玩家ID                               |
| totalScore | 15           | 是       | Number   | 总得分                               |
| mineHits   | 2            | 是       | Number   | 踩雷数（用于排名，标记后踩雷不计入） |
| rank       | 1/2/3/4      | 是       | Number   | 最终排名                             |
| coinChg    | 180          | 是       | Number   | 金币变化（正数为获得，0为无变化）    |

#### 响应示例

```json
{
  "msgId": "Settlement",
  "code": 0,
  "msg": "success",
  "data": {
    "gameType": "minesweeper",
    "totalRounds": 5,
    "playerCount": 2,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 25,
        "totalCoin": 180,
        "mineHits": 1,
        "rank": 1,
        "coinChg": 180
      },
      {
        "userId": "player_002",
        "totalScore": 15,
        "totalCoin": 0,
        "mineHits": 2,
        "rank": 2,
        "coinChg": 0
      }
    ],
    "fee": 100,
    "gameStats": {
      "mapSize": "8x8",
      "mineCount": 13,
      "revealedCount": 45
    }
  }
}
```

## 关卡模式游戏接口

### 30. 开始关卡游戏

**WebSocket消息类型**：`"ExtendLevelInfo"`

开始指定关卡的单人游戏，创建专用游戏房间并返回游戏信息。

**接口说明**：
- 发送此消息即代表开始游戏，无需额外的开始游戏标识
- 如果用户已有进行中的关卡游戏，返回现有房间信息（断线重连）

**游戏特点**：
- 单人游戏模式，无机器人参与
- 无回合制，玩家可随时操作
- 无倒计时限制
- 游戏结束时自动推送通知和更新进度

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| levelId | 1-30 | 是 | Number | 关卡编号 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| levelId | 1 | 是 | Number | 关卡编号 |
| mapType | 0 | 是 | Number | 地图类型（0=方格，1=六边形） |
| mapWidth | 8 | 否 | Number | 地图宽度（方格地图用） |
| mapHeight | 8 | 否 | Number | 地图高度（方格地图用） |
| validHexes | [...] | 否 | Array | 有效六边形坐标（六边形地图用） |
| neighborMap | {...} | 否 | Object | 邻居关系映射（六边形地图用） |
| mineCount | 8 | 是 | Number | 地雷数量 |
| isSpecial | true/false | 是 | Boolean | 是否特殊关卡 |
| isUnlocked | true/false | 是 | Boolean | 是否已解锁 |
| isCleared | true/false | 是 | Boolean | 是否已通关 |
| roomId | 1640995200123 | 是 | Number | 游戏房间ID（用于断线重连） |
| gameStatus | 2 | 是 | Number | 游戏状态 |
| countDown | -1 | 是 | Number | 倒计时（关卡模式固定为-1） |

#### 请求示例

```json
{
  "msgId": "ExtendLevelInfo",
  "data": {
    "levelId": 1
  }
}
```

#### 响应示例

```json
{
  "msgId": "ExtendLevelInfo",
  "code": 0,
  "msg": "success",
  "data": {
    "levelId": 1,
    "mapType": 0,
    "mapWidth": 8,
    "mapHeight": 8,
    "mineCount": 8,
    "isSpecial": false,
    "isUnlocked": true,
    "isCleared": false,
    "roomId": 1640995200123,
    "gameStatus": 2,
    "countDown": -1
  }
}
```

### 31. 获取关卡进度

**WebSocket消息类型**：`"ExtendLevelProgress"`

获取用户当前的关卡进度信息。

#### 请求参数

无参数

#### 响应参数

| 参数名        | 参数值 | 是否必填 | 参数类型 | 描述说明         |
| ------------- | ------ | -------- | -------- | ---------------- |
| totalLevels   | 30     | 是       | Number   | 总关卡数         |
| clearedLevels | 5      | 是       | Number   | 已通关关卡数     |
| currentLevel  | 6      | 是       | Number   | 当前应挑战的关卡 |

#### 响应示例

```json
{
  "msgId": "ExtendLevelProgress",
  "code": 0,
  "msg": "success",
  "data": {
    "totalLevels": 30,
    "clearedLevels": 5,
    "currentLevel": 6
  }
}
```



### 32. 关卡点击方块

**WebSocket消息类型**：`"LevelClickBlock"`

关卡模式专用的点击方块接口，与联机模式完全独立。支持自由的标记状态切换和即时操作响应。

**接口特点**：
- **独立接口**：与联机模式的`ClickBlock`接口完全分离
- **即时响应**：操作立即生效，无延迟展示
- **自由标记**：支持标记状态的自由切换（标记↔取消标记）
- **无时间限制**：没有回合制和倒计时限制
- **单人游戏**：专为关卡模式设计的单人操作

#### 请求参数

**坐标参数根据地图类型而不同**：

##### 方格地图（mapType=0）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | 方块x坐标（0-7，从左到右） |
| y | - | 是 | Number | 方块y坐标（0-7，从下到上，左下角为(0,0)） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

##### 六边形地图（mapType=1）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| q | - | 是 | Number | 六边形q坐标（轴向坐标系统） |
| r | - | 是 | Number | 六边形r坐标（轴向坐标系统） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

**注意**：六边形地图的坐标通过x和y参数传递，其中x对应q坐标，y对应r坐标。

#### 请求示例

##### 方格地图挖掘操作
```json
{
  "msgId": "LevelClickBlock",
  "data": {
    "x": 3,
    "y": 2,
    "action": 1
  }
}
```

##### 方格地图标记操作
```json
{
  "msgId": "LevelClickBlock",
  "data": {
    "x": 5,
    "y": 4,
    "action": 2
  }
}
```

##### 六边形地图操作
```json
{
  "msgId": "LevelClickBlock",
  "data": {
    "x": 1,
    "y": -1,
    "action": 1
  }
}
```

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| success | true/false | 是 | Boolean | 操作是否成功 |
| x | 3 | 是 | Number | 操作坐标x（六边形地图中对应q坐标） |
| y | 2 | 是 | Number | 操作坐标y（六边形地图中对应r坐标） |
| action | 1 | 是 | Number | 操作类型（1-挖掘，2-标记） |
| result | 2/"mine"/"marked"/"unmarked" | 是 | Number/String | 操作结果 |
| floodFillResults | - | 否 | Array | 连锁展开结果列表，参考FloodFillResult结构 |
| remainingMines | 8 | 是 | Number | 剩余地雷数量 |
| message | "操作成功" | 是 | String | 提示信息 |

#### 操作结果说明

- **挖掘操作**：
  - 返回周围地雷数（0-8或0-6）
  - 如果是地雷则返回"mine"
  - 空白方块会触发连锁展开
- **标记操作**：
  - 返回"marked"（已标记）或"unmarked"（已取消标记）
  - 不透露该位置是否为地雷，保持游戏悬念
  - 支持自由切换标记状态

#### 标记功能特点

- **自由切换**：可以在标记和未标记状态之间自由切换
- **状态保护**：已挖掘的方块无法标记，已标记的方块无法挖掘
- **悬念保持**：标记操作不会透露方块是否为地雷
- **即时生效**：标记操作立即生效，无需等待

#### 游戏结束处理

- **输赢通知**：游戏结束时（胜利或失败）会自动推送`LevelGameEnd`消息
- **响应简化**：`LevelClickBlock`响应中不再包含`gameStatus`字段
- **状态分离**：操作结果和游戏状态通过不同消息分别处理

#### 响应示例

**挖掘数字方块：**
```json
{
  "msgId": "LevelClickBlock",
  "code": 0,
  "msg": "success",
  "data": {
    "success": true,
    "x": 3,
    "y": 2,
    "action": 1,
    "result": 2,
    "remainingMines": 8,
    "message": "挖掘成功，周围有2颗地雷"
  }
}
```

**挖掘空白方块（触发连锁展开）：**
```json
{
  "msgId": "LevelClickBlock",
  "code": 0,
  "msg": "success",
  "data": {
    "success": true,
    "x": 1,
    "y": 1,
    "action": 1,
    "result": 0,
    "floodFillResults": [
      {"x": 0, "y": 1, "neighborMines": 1},
      {"x": 2, "y": 1, "neighborMines": 0},
      {"x": 1, "y": 0, "neighborMines": 1},
      {"x": 1, "y": 2, "neighborMines": 2}
    ],
    "remainingMines": 8,
    "message": "挖掘成功，触发连锁展开"
  }
}
```

**踩雷失败：**
```json
{
  "msgId": "LevelClickBlock",
  "code": 0,
  "msg": "success",
  "data": {
    "success": true,
    "x": 4,
    "y": 3,
    "action": 1,
    "result": "mine",
    "remainingMines": 8,
    "message": "踩雷了！游戏失败"
  }
}
```

**标记方块：**
```json
{
  "msgId": "LevelClickBlock",
  "code": 0,
  "msg": "success",
  "data": {
    "success": true,
    "x": 5,
    "y": 4,
    "action": 2,
    "result": "marked",
    "remainingMines": 8,
    "message": "标记成功"
  }
}
```

**取消标记：**
```json
{
  "msgId": "LevelClickBlock",
  "code": 0,
  "msg": "success",
  "data": {
    "success": true,
    "x": 5,
    "y": 4,
    "action": 2,
    "result": "unmarked",
    "remainingMines": 8,
    "message": "取消标记"
  }
}
```

### 33. 离开房间

**WebSocket消息类型**：`"LeaveRoom"`

在关卡模式下，玩家可以使用此接口主动退出关卡游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| isConfirmLeave | true | 是 | Boolean | 确认离开房间 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| levelId | 1 | 是 | Number | 关卡编号 |
| message | "已退出关卡游戏" | 是 | String | 提示信息 |

#### 响应示例

```json
{
  "msgId": "LeaveRoom",
  "code": 0,
  "msg": "success",
  "data": {
    "levelId": 1,
    "message": "已退出关卡游戏"
  }
}
```

### 34. 关卡游戏结束通知

**WebSocket消息类型**：`"LevelGameEnd"`

服务端在关卡游戏自然结束时（胜利或失败）自动推送的通知消息。

**发送时机**：
- 踩雷失败时自动推送
- 完成所有非地雷方块时自动推送
- 在ClickBlock响应之后立即推送
- 自动更新用户进度（胜利时）

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| levelId | 1 | 是 | Number | 关卡编号 |
| isSuccess | true/false | 是 | Boolean | 是否胜利 |
| message | "恭喜通关第1关！" | 是 | String | 提示信息 |
| nextLevelId | 2 | 否 | Number | 下一关编号（胜利且有下一关时） |

#### 通知示例

**胜利通知**：
```json
{
  "msgId": "LevelGameEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "levelId": 1,
    "isSuccess": true,
    "message": "恭喜通关第1关！",
    "nextLevelId": 2
  }
}
```

**失败通知**：
```json
{
  "msgId": "LevelGameEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "levelId": 1,
    "isSuccess": false,
    "message": "第1关挑战失败，请再试一次！"
  }
}
```

### 35. AI托管状态变更通知

**WebSocket消息类型**：`"AIStatusChange"`

服务端通知客户端AI托管状态发生变更。当房间内任何玩家进入或退出AI托管时，会向所有玩家广播此通知。

**系统特点**：

- **仅联机模式**：关卡模式不支持AI托管
- **自动触发**：倒计时剩余5秒时，系统自动检查未操作的玩家并进入AI托管
- **自动退出**：玩家进行任何操作（点击方块）时自动退出托管
- **随机决策**：AI托管使用纯随机决策，不使用智能算法
- **状态广播**：托管状态变更会实时广播给房间内所有玩家

#### 触发时机

**自动进入AI托管**：

- 回合倒计时剩余5秒时，系统自动检查未操作的玩家
- 如果玩家在当前回合未进行任何操作，自动进入AI托管

**自动退出AI托管**：

- 玩家进行任何点击方块操作时，立即退出AI托管状态

#### 通知参数

| 参数名      | 参数值     | 是否必填 | 参数类型 | 描述说明                                |
| ----------- | ---------- | -------- | -------- | --------------------------------------- |
| userId      | "user123"  | 是       | String   | 状态变更的用户ID                        |
| isAIManaged | true       | 是       | Boolean  | 是否进入AI托管（true=进入，false=退出） |
| timestamp   | 1640995200 | 是       | Number   | 状态变更时间戳                          |

#### 通知示例

**进入AI托管通知**：

```json
{
  "msgId": "AIStatusChange",
  "code": 0,
  "msg": "success",
  "data": {
    "userId": "user123",
    "isAIManaged": true,
    "timestamp": 1640995200
  }
}
```

**退出AI托管通知**：

```json
{
  "msgId": "AIStatusChange",
  "code": 0,
  "msg": "success",
  "data": {
    "userId": "user123",
    "isAIManaged": false,
    "timestamp": 1640995260
  }
}
```

## 调试接口

### 显示地雷位置

**WebSocket消息类型**：`"DebugShowMines"`

**⚠️ 注意：这是调试接口，仅用于开发测试，生产环境应禁用**

用于查看当前地图中所有地雷的位置，帮助验证地雷分布和游戏逻辑。

#### 使用场景

- 开发阶段验证地雷生成算法
- 测试游戏逻辑正确性
- 调试地图配置问题

#### 权限控制

- 联机模式和关卡模式均可用
- 无需特殊权限验证（调试用途）

#### 请求参数

无需参数，直接发送消息即可。

#### 响应参数

| 参数名 | 参数值       | 是否必填 | 参数类型 | 描述说明           |
| ------ | ------------ | -------- | -------- | ------------------ |
| mines  | 地雷位置数组 | 是       | Array    | 所有地雷的坐标位置 |

#### 地雷位置格式

**方格地图**：

```json
[
  {"x": 1, "y": 2},
  {"x": 3, "y": 4},
  {"x": 0, "y": 1}
]
```

**六边形地图**：

```json
[
  {"q": 1, "r": 2},
  {"q": 3, "r": 4},
  {"q": 0, "r": -1}
]
```

#### 响应示例

**请求**：

```json
{
  "msgId": "DebugShowMines"
}
```

**方格地图响应**：

```json
{
  "msgId": "DebugShowMines",
  "code": 0,
  "msg": "success",
  "data": {
    "mines": [
      {"x": 2, "y": 1},
      {"x": 4, "y": 3},
      {"x": 1, "y": 4},
      {"x": 6, "y": 2},
      {"x": 3, "y": 5}
    ]
  }
}
```

**六边形地图响应**：

```json
{
  "msgId": "DebugShowMines",
  "code": 0,
  "msg": "success",
  "data": {
    "mines": [
      {"q": 1, "r": -1},
      {"q": -2, "r": 1},
      {"q": 0, "r": 2},
      {"q": 3, "r": -2},
      {"q": -1, "r": 0}
    ]
  }
}
```





## 数据结构定义

### VoiceRoomUser 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| userId | String | 玩家id |
| nickName | String | 昵称 |
| avatar | String | 头像 |
| pos | Number | 座位号 |
| ready | Boolean | 是否准备 |
| robot | Boolean | 是否机器人 |
| role | String | 角色 |

### RoomUser 结构

RoomUser 采用组合模式设计，包含以下几个组件：

#### 基础用户信息（UserBaseInfo）

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| userId | String | 是 | 用户ID |
| nickName | String | 是 | 昵称 |
| avatar | String | 是 | 头像 |
| pos | Number | 是 | 座位号 |
| coin | Number | 是 | 玩家最新金币 |
| status | Number | 是 | 玩家状态（1-站立，2-已坐下，3-游戏中） |

#### 游戏状态信息（GameStateInfo）

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| gameScore | Number | 是 | 当前游戏得分 |
| isAlive | Boolean | 是 | 是否还在游戏中 |
| isAIManaged | Boolean | 是 | 是否处于AI托管状态（仅联机模式） |

#### 操作信息（OperationInfo）

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| operationX | Number | 否 | 操作坐标X |
| operationY | Number | 否 | 操作坐标Y |
| operationType | Number | 否 | 操作类型 |
| isFirstChoice | Boolean | 否 | 是否首选玩家 |

#### 展示信息（DisplayInfo）

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| currentRound | Number | 是 | 当前回合数 |
| floodFillResults | Array | 否 | 连锁展开结果，参考FloodFillResult结构 |

**注意**：
- 基础用户信息始终存在
- 游戏状态信息仅在联机模式下存在
- 操作信息根据游戏状态和权限控制可见性
- 展示信息仅在展示阶段存在

### FloodFillResult 结构

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| triggerX | Number | 是 | 触发连锁展开的X坐标 |
| triggerY | Number | 是 | 触发连锁展开的Y坐标 |
| revealedCells | Array | 是 | 连锁展开的方块列表，参考RevealedCellInfo结构 |

### RevealedCellInfo 结构

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| x | Number | 是 | X坐标 |
| y | Number | 是 | Y坐标 |
| neighborMines | Number | 是 | 周围地雷数 |

### InviteInfo 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| inviteCode | Number | 邀请码 |
| playerNum | Number | 玩家人数 |
| mapType | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | Number | 房间费用 |
| users | Array | 邀请房间中的玩家列表 |

## 错误码说明

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| 1 | 错误的GameId | 游戏ID错误 |
| 6 | 玩家已经在匹配队列中 | 重复匹配 |
| 7 | 没有足够的金币 | 金币不足 |
| 10 | 没有找到玩家信息 | 玩家不存在 |
| 13 | 请求参数错误 | 参数验证失败 |
| 21 | 游客被限制 | 游客无法进行此操作 |
| 32 | 玩家已经在游戏中了 | 重复进入游戏 |

## 游戏状态说明

**扫雷游戏状态（mapType=0）**：

| 状态值 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 扫雷进行中 | 扫雷游戏进行中，玩家可以进行操作 |
| 1 | 回合结束展示 | 回合结束，展示所有玩家操作结果 |
| 2 | 游戏结束 | 扫雷游戏已结束 |

## 地图类型说明

| 类型值 | 类型名称 | 描述 |
|--------|----------|----------|
| 0 | 方格地图 | 8×8方格布局，13个地雷随机分布 |
| 1 | 六边形地图 | 每一块为六边形布局的地图 |

## 扫雷游戏操作类型说明

| 操作值 | 操作名称 | 描述 |
|--------|----------|------|
| 1 | 挖掘 | 挖掘方块，揭示内容（安全区+6分，地雷-12分） |
| 2 | 标记地雷 | 标记或取消标记地雷（正确标记+10分，错误标记+0分） |

## 扫雷游戏时间配置说明

| 配置项 | 时间（秒） | 描述 |
|--------|------------|------|
| 游戏开始延迟 | 2 | GameStart后的展示时间 |
| 回合总时间 | 25 | 每回合的总操作时间 |
| 隐藏期 | 20 | 前20秒只能看到自己的操作 |
| 展示期 | 5 | 后5秒显示所有人的操作 |

## 扫雷游戏得分规则说明

| 操作结果 | 得分 | 描述 |
|----------|------|------|
| 挖掘安全区 | +6分 | 成功挖掘到非地雷方块 |
| 挖掘地雷区 | -12分 | 不幸挖掘到地雷 |
| 正确标记地雷 | +10分 | 标记的方块确实是地雷 |
| 错误标记 | +0分 | 标记的方块不是地雷 |

**注意**：
- 多个玩家可以选择同一个方块，都会获得相同的得分
- 每回合每个玩家只能操作一次
- 游戏结束条件：所有非地雷方块都被至少一个玩家挖掘过

## AI托管系统接口

## AI托管系统说明

### 自动触发机制

#### 进入条件
- **时机**：玩家第一次超时时自动触发
- **条件**：玩家连续超时次数从0变为1时
- **范围**：仅联机模式，关卡模式不支持AI托管
- **状态**：系统自动调用`StartAIManagement()`方法
- **立即操作**：进入AI托管后立即执行一次随机操作

#### 退出条件
- **触发方式1**：玩家进行任何点击方块操作
  - **处理**：在`OnUserClickBlock()`方法开始时检查并退出托管
- **触发方式2**：玩家重新上线（发送任何消息）
  - **处理**：在`UpdateLastMsg()`方法中检查并退出托管
- **状态**：系统自动调用`StopAIManagement()`方法

---

## 断线重连机制

### 联机模式断线重连

#### 触发条件
- 玩家重新进入游戏时，系统检查是否有未完成的游戏
- 基于本地房间信息和Redis游戏状态进行判断

#### 排除条件
- **主动离开的玩家**：使用`LeaveRoom`主动退出的玩家不会触发断线重连
- **房间不存在**：目标房间已被删除
- **用户不在房间**：用户不在目标房间中
- **房间无效**：房间状态异常或已停止

#### 设计原则
- **房间持续性**：玩家主动退出后，房间继续存在，其他玩家可以继续游戏
- **状态隔离**：已离开的玩家不会重新连接到原房间
- **游戏连续性**：确保游戏进程不因个别玩家离开而中断

---

## 游戏模式标识

### IsOnlineMode字段说明

在所有EnterRoom响应和断线重连响应中，都包含`isOnlineMode`字段用于标识当前游戏模式：

#### 字段定义
- **字段名**：`isOnlineMode`
- **类型**：Boolean
- **必填**：是
- **取值**：
  - `true`：联机模式（多人在线游戏）
  - `false`：关卡模式（单人关卡游戏）

#### 使用场景
1. **前端界面适配**：根据模式显示不同的UI元素
2. **功能开关**：某些功能仅在特定模式下可用
3. **逻辑分支**：前端根据模式执行不同的游戏逻辑
4. **状态管理**：帮助前端正确管理游戏状态

#### 判断逻辑
- **联机模式**：`!room.IsLevelMode`（非关卡模式即为联机模式）
- **关卡模式**：`room.IsLevelMode`（明确标记为关卡模式）

#### 响应位置
- **正常进入房间**：EnterRoom响应的根级字段
- **断线重连**：断线重连响应的根级字段
- **所有模式**：联机模式和关卡模式都包含此字段

#### 持续性
- **后续回合**：一旦进入AI托管，后续所有回合自动保持AI托管状态
- **无需重新触发**：不需要每回合重新检查是否进入AI托管
- **重复触发**：玩家上线后再次离线超时时，可以重新触发AI托管
  - 玩家重新上线时会自动退出AI托管并重置超时计数
  - 再次第一次超时时会重新触发AI托管机制

### 托管行为

#### 操作策略
- **决策类型**：纯随机决策，不使用智能算法
- **操作概率**：70%概率挖掘，30%概率标记
- **操作时机**：在回合前5秒内（倒计时20-25秒）随机选择时间点操作
- **执行概率**：每秒30%概率执行操作

#### 操作执行
```javascript
// AI托管操作时机
if (countDown >= 20 && countDown <= 25) {
    // 30%概率在当前秒执行操作
    if (Math.random() < 0.3) {
        performAIOperation();
    }
}
```

### 状态管理

#### 用户状态字段
- **IsAIManaged**：是否处于AI托管状态
- **AIStartTime**：AI托管开始时间戳
- **AIOperationCount**：AI代操作次数

#### 状态同步
- **实时广播**：状态变更时立即广播给房间内所有玩家
- **断线重连**：重连时会同步当前的AI托管状态
- **房间清理**：房间结束时自动清理所有AI托管状态

### 游戏影响

#### 操作规则
- **正常计分**：AI托管的操作按正常规则计分
- **首选奖励**：AI托管状态下不享受首选玩家+1分奖励
- **操作展示**：AI托管的操作会在操作展示阶段正常显示

#### 显示效果
- **托管标识**：房间内其他玩家可以看到该玩家处于AI托管状态
- **状态图标**：客户端可根据`isAIManaged`字段显示托管图标
- **操作提示**：托管期间的操作会标识为AI代操作

### 使用场景

#### 适用情况
- **临时离开**：玩家需要临时离开但不想影响游戏进度
- **网络延迟**：网络问题导致操作困难时的自动保护
- **操作超时**：玩家未能在规定时间内操作的自动处理

#### 系统保护
- **防止卡局**：确保游戏能够正常进行，不会因个别玩家未操作而卡住
- **公平性**：AI托管使用随机决策，不会给玩家带来不公平的优势
- **用户体验**：减少因等待超时玩家而影响其他玩家的游戏体验

---

